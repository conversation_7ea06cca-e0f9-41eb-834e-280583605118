const { Events, EmbedBuilder } = require('discord.js');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton() || !interaction.customId.startsWith('rps_')) return;

        const [_, userId, playerChoice] = interaction.customId.split('_');
        
        if (interaction.user.id !== userId) {
            return interaction.reply({ content: 'This is not your game!', ephemeral: true });
        }

        const choices = ['rock', 'paper', 'scissors'];
        const botChoice = choices[Math.floor(Math.random() * choices.length)];

        const getEmoji = (choice) => {
            switch (choice) {
                case 'rock': return '🪨';
                case 'paper': return '📄';
                case 'scissors': return '✂️';
            }
        };

        const determineWinner = (player, bot) => {
            if (player === bot) return 'tie';
            if (
                (player === 'rock' && bot === 'scissors') ||
                (player === 'paper' && bot === 'rock') ||
                (player === 'scissors' && bot === 'paper')
            ) return 'player';
            return 'bot';
        };

        const result = determineWinner(playerChoice, botChoice);
        const resultText = {
            tie: "It's a tie!",
            player: 'You win! 🎉',
            bot: 'Bot wins! 🤖'
        };

        const embed = new EmbedBuilder()
            .setTitle('Rock, Paper, Scissors - Result')
            .setDescription(
                `Your choice: ${getEmoji(playerChoice)} ${playerChoice}\n` +
                `Bot's choice: ${getEmoji(botChoice)} ${botChoice}\n\n` +
                `${resultText[result]}`
            )
            .setColor(result === 'player' ? '#00FF00' : result === 'bot' ? '#FF0000' : '#2B2D31')
            .setFooter({ text: `Player: ${interaction.user.tag}` });

        await interaction.update({ embeds: [embed], components: interaction.message.components });
    },
};