:root {
    --primary-color: #7289da;
    --secondary-color: #2c2f33;
    --text-color: #2f3136;
    --bg-color: #ffffff;
    --card-bg: #f6f6f6;
    --transition: all 0.3s ease;
}

[data-theme="dark"] {
    --primary-color: #7289da;
    --secondary-color: #23272a;
    --text-color: #ffffff;
    --bg-color: #2f3136;
    --card-bg: #36393f;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: var(--transition);
}

nav {
    background-color: var(--secondary-color);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
}

.logo i {
    font-size: 2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.nav-links a:hover {
    color: var(--primary-color);
}

#darkModeToggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.5rem;
}

.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--secondary-color);
    color: white;
    padding: 2rem;
}

.hero-content {
    max-width: 800px;
    text-align: center;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.cta-button {
    padding: 1rem 2rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
}

.cta-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.cta-button.secondary {
    background-color: transparent;
    border: 2px solid white;
    color: white;
}

.stats-preview {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat .number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat .label {
    font-size: 1rem;
    color: #ffffff80;
}

#features {
    padding: 6rem 2rem;
    background-color: var(--bg-color);
}

#features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

#commands {
    padding: 6rem 2rem;
    background-color: var(--secondary-color);
    color: white;
}

#commands h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.commands-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.command-card {
    background-color: var(--card-bg);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.command-card code {
    display: inline-block;
    background-color: var(--secondary-color);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 4rem 2rem 2rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    font-size: 1.5rem;
}

.footer-bottom {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ffffff20;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 6px;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: white;
    transition: var(--transition);
}

@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--secondary-color);
        flex-direction: column;
        padding: 1rem;
        text-align: center;
    }

    .nav-links.active {
        display: flex;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .stats-preview {
        flex-direction: column;
        gap: 2rem;
    }
}
