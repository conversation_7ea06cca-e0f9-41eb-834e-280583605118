const { Events, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionsBitField, ChannelType, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Create directories for transcripts and JSON fallback
const TRANSCRIPTS_DIR = path.join(__dirname, '../transcripts');
const JSON_FALLBACK_DIR = path.join(__dirname, '../data/tickets');
[TRANSCRIPTS_DIR, JSON_FALLBACK_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// Define MongoDB Schemas
const ticketSchema = new mongoose.Schema({
    ticketId: String,
    userId: String,
    userTag: String,
    channelId: String,
    type: String,
    reason: String,
    status: {
        type: String,
        enum: ['open', 'closed'],
        default: 'open'
    },
    createdAt: { type: Date, default: Date.now },
    closedAt: Date,
    closedBy: String,
    transcript: String
});

const Ticket = mongoose.models.Ticket || mongoose.model('Ticket', ticketSchema);

// Helper function to save ticket data
async function saveTicketData(ticketData) {
    if (mongoose.connection.readyState === 1) {
        try {
            const ticket = new Ticket(ticketData);
            await ticket.save();
            return true;
        } catch (error) {
            console.error('MongoDB save error:', error);
            return false;
        }
    }
    return false;
}

// Helper function to save as JSON fallback
function saveAsJson(data, filename) {
    const filepath = path.join(JSON_FALLBACK_DIR, filename);
    fs.writeFileSync(filepath, JSON.stringify(data, null, 2));
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        try {
            const logChannelId = '1253579753621950546';
            const supportRoleId = '1237979199747129396';

            // Handle ticket creation from dropdown
            if (interaction.isStringSelectMenu() && interaction.customId === 'supportOptions') {
                const selectedValue = interaction.values[0];

                if (selectedValue === 'st') {
                    const modal = new ModalBuilder()
                        .setCustomId('ticketReasonModal')
                        .setTitle('Ticket Reason');

                    const reasonInput = new TextInputBuilder()
                        .setCustomId('reasonInput')
                        .setLabel('Reason for the ticket')
                        .setStyle(TextInputStyle.Paragraph)
                        .setPlaceholder('Type your reason here...')
                        .setRequired(true)
                        .setMaxLength(1000);

                    modal.addComponents(new ActionRowBuilder().addComponents(reasonInput));
                    await interaction.showModal(modal);
                    return;
                }

                if (selectedValue === 'bp') {
                    await handlePurchaseTicket(interaction, logChannelId, supportRoleId);
                }
            }

            // Handle modal submission for support ticket
            if (interaction.isModalSubmit() && interaction.customId === 'ticketReasonModal') {
                await handleSupportTicket(interaction, logChannelId, supportRoleId);
            }

            // Handle ticket closing
            if (interaction.isButton()) {
                if (interaction.customId === 'closeTicket' || interaction.customId === 'closePurchaseTicket') {
                    await handleCloseConfirmation(interaction);
                }

                if (interaction.customId === 'confirmClose') {
                    await handleTicketClose(interaction, logChannelId);
                }
            }
        } catch (error) {
            console.error('Error in ticket handler:', error);
            try {
                const errorMessage = 'An error occurred while processing your request.';
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({ content: errorMessage, ephemeral: true });
                } else {
                    await interaction.editReply({ content: errorMessage });
                }
            } catch (e) {
                console.error('Error sending error message:', e);
            }
        }
    }
};

async function handlePurchaseTicket(interaction, logChannelId, supportRoleId) {
    await interaction.deferReply({ ephemeral: true });
    
    const purchaseChannel = await createTicketChannel(interaction, 'purchase', supportRoleId);
    const ticketData = {
        ticketId: `PURCHASE-${Date.now()}`,
        userId: interaction.user.id,
        userTag: interaction.user.tag,
        channelId: purchaseChannel.id,
        type: 'purchase',
        reason: 'Bot Purchase Request',
        status: 'open'
    };

    const mongoSaved = await saveTicketData(ticketData);
    if (!mongoSaved) {
        saveAsJson(ticketData, `ticket-${ticketData.ticketId}.json`);
    }

    const purchaseEmbed = createTicketEmbed(interaction, 'Premium Purchase', 'Bot Purchase Request');
    const closeButton = createCloseButton('closePurchaseTicket');

    await purchaseChannel.send({
        content: `<@${interaction.user.id}>, <@&${supportRoleId}>`,
        embeds: [purchaseEmbed],
        components: [new ActionRowBuilder().addComponents(closeButton)]
    });

    await logTicketCreation(interaction, logChannelId, purchaseChannel, 'Bot Purchase Request');
    await interaction.editReply({ content: 'Your bot purchase ticket has been opened!', ephemeral: true });
}

async function handleSupportTicket(interaction, logChannelId, supportRoleId) {
    const reason = interaction.fields.getTextInputValue('reasonInput');
    await interaction.deferReply({ ephemeral: true });

    const supportChannel = await createTicketChannel(interaction, 'ticket', supportRoleId);
    const ticketData = {
        ticketId: `SUPPORT-${Date.now()}`,
        userId: interaction.user.id,
        userTag: interaction.user.tag,
        channelId: supportChannel.id,
        type: 'support',
        reason: reason,
        status: 'open'
    };

    const mongoSaved = await saveTicketData(ticketData);
    if (!mongoSaved) {
        saveAsJson(ticketData, `ticket-${ticketData.ticketId}.json`);
    }

    const ticketEmbed = createTicketEmbed(interaction, 'Ticket Opened', reason);
    const closeButton = createCloseButton('closeTicket');

    await supportChannel.send({
        content: `<@${interaction.user.id}>, <@&${supportRoleId}>`,
        embeds: [ticketEmbed],
        components: [new ActionRowBuilder().addComponents(closeButton)]
    });

    await logTicketCreation(interaction, logChannelId, supportChannel, reason);
    await interaction.editReply({ content: 'Your ticket has been opened.', ephemeral: true });
}

async function handleCloseConfirmation(interaction) {
    const confirmationEmbed = new EmbedBuilder()
        .setColor('#2F3136')
        .setTitle('Close Ticket')
        .setDescription('Are you sure you want to close this ticket? This action cannot be undone.');

    const finalCloseButton = new ButtonBuilder()
        .setCustomId('confirmClose')
        .setLabel('Confirm Close')
        .setStyle(ButtonStyle.Danger);

    await interaction.reply({
        embeds: [confirmationEmbed],
        components: [new ActionRowBuilder().addComponents(finalCloseButton)],
        ephemeral: true
    });
}

async function handleTicketClose(interaction, logChannelId) {
    const ticketChannel = interaction.channel;
    const messages = await ticketChannel.messages.fetch({ limit: 100 });
    const transcript = createTranscript(messages);
    
    const transcriptFilePath = path.join(TRANSCRIPTS_DIR, `${ticketChannel.name}-${Date.now()}.html`);
    fs.writeFileSync(transcriptFilePath, transcript);

    // Update ticket in MongoDB or JSON
    const closeData = {
        status: 'closed',
        closedAt: new Date(),
        closedBy: interaction.user.id,
        transcript: transcript
    };

    if (mongoose.connection.readyState === 1) {
        try {
            await Ticket.findOneAndUpdate(
                { channelId: ticketChannel.id },
                closeData,
                { new: true }
            );
        } catch (error) {
            console.error('MongoDB update error:', error);
            saveAsJson({
                channelId: ticketChannel.id,
                ...closeData
            }, `ticket-close-${ticketChannel.id}-${Date.now()}.json`);
        }
    } else {
        saveAsJson({
            channelId: ticketChannel.id,
            ...closeData
        }, `ticket-close-${ticketChannel.id}-${Date.now()}.json`);
    }

    const closeEmbed = createCloseEmbed(interaction);

    try {
        await interaction.user.send({
            content: 'Here is the transcript for your ticket:',
            files: [{ attachment: transcriptFilePath, name: 'transcript.html' }],
            embeds: [closeEmbed]
        });
    } catch (error) {
        console.error('Could not send DM to user:', error);
    }

    await logTicketClose(interaction, logChannelId, ticketChannel);
    await ticketChannel.delete();
}

function createTicketChannel(interaction, prefix, supportRoleId) {
    return interaction.guild.channels.create({
        name: `${prefix}-${interaction.user.username}`,
        type: ChannelType.GuildText,
        permissionOverwrites: [
            {
                id: interaction.guild.id,
                deny: [PermissionsBitField.Flags.ViewChannel],
            },
            {
                id: interaction.user.id,
                allow: [PermissionsBitField.Flags.ViewChannel],
            },
            {
                id: supportRoleId,
                allow: [PermissionsBitField.Flags.ViewChannel],
            },
        ],
    });
}

function createTicketEmbed(interaction, title, reason) {
    return new EmbedBuilder()
        .setColor('#2F3136')
        .setTitle(title)
        .setDescription(`Hello <@${interaction.user.id}>, your ticket has been opened. Please wait for staff to assist you.\nReason: ${reason}`)
        .setFooter({ text: `Ticket opened by: ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });
}

function createCloseButton(customId) {
    return new ButtonBuilder()
        .setCustomId(customId)
        .setLabel('Close Ticket')
        .setStyle(ButtonStyle.Danger);
}

function createTranscript(messages) {
    const transcript = [];
    messages.forEach(msg => {
        if (!msg.author.bot) {
            transcript.push({
                username: msg.author.username,
                content: msg.content,
                timestamp: msg.createdAt,
                avatar: msg.author.displayAvatarURL(),
            });
        }
    });

    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Ticket Transcript</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #2F3136;
                    color: #f3f3f3;
                    padding: 20px;
                    line-height: 1.6;
                }
                .message {
                    background: #36393f;
                    padding: 10px;
                    margin: 10px 0;
                    border-radius: 5px;
                }
                .timestamp {
                    color: #72767d;
                    font-size: 0.8em;
                }
            </style>
        </head>
        <body>
            <h1>Ticket Transcript</h1>
            ${transcript.map(msg => `
                <div class="message">
                    <strong>${msg.username}</strong>
                    <span class="timestamp">${new Date(msg.timestamp).toLocaleString()}</span>
                    <div>${msg.content}</div>
                </div>
            `).join('')}
        </body>
        </html>
    `;
}

function createCloseEmbed(interaction) {
    return new EmbedBuilder()
        .setColor('#2F3136')
        .setTitle('Ticket Closed')
        .setDescription(`Ticket has been closed.\n\n**Opened by:** ${interaction.user.tag}\n**Opened at:** ${interaction.channel.createdAt}\n**Closed at:** ${new Date()}`)
        .setFooter({ text: `Ticket closed by: ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });
}

async function logTicketCreation(interaction, logChannelId, ticketChannel, reason) {
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
        const logEmbed = new EmbedBuilder()
            .setColor('#2F3136')
            .setTitle('Ticket Opened')
            .setDescription(`A new ticket has been opened by <@${interaction.user.id}> in <#${ticketChannel.id}>.\nReason: ${reason}`)
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    }
}

async function logTicketClose(interaction, logChannelId, ticketChannel) {
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
        const logEmbed = new EmbedBuilder()
            .setColor('#2F3136')
            .setTitle('Ticket Closed')
            .setDescription(`Ticket closed by <@${interaction.user.id}> in <#${ticketChannel.id}>.\n**Opened by:** ${interaction.user.tag}\n**Opened at:** ${ticketChannel.createdAt}\n**Closed at:** ${new Date()}`)
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    }
};
