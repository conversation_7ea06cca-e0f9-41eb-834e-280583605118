{"version": 3, "file": "webhook.d.ts", "sourceRoot": "", "sources": ["webhook.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,EACX,cAAc,EACd,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,0BAA0B,EAC1B,YAAY,EACZ,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,MAAM,WAAW,UAAU;IAC1B;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;IAClB;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,UAAU,EAAE,SAAS,CAAC;IACtB;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;IACf;;OAEG;IACH,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB;;OAEG;IACH,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,cAAc,EAAE,SAAS,GAAG,IAAI,CAAC;IACjC;;OAEG;IACH,YAAY,CAAC,EAAE,eAAe,CAAC;IAC/B;;OAEG;IACH,cAAc,CAAC,EAAE,iBAAiB,CAAC;IACnC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,GACxB,mBAAmB,CAAC,sBAAsB,CAAC,KAAK,EAAE,mBAAmB,CAAC,GACtE,mBAAmB,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAC5B,wBAAwB,CACxB,2BAA2B,CAAC,qBAAqB,EACjD,wCAAwC,CACvC,GACD,wBAAwB,CAAC,2BAA2B,CAAC,iBAAiB,EAAE,oCAAoC,CAAC,GAC7G,wBAAwB,CAAC,2BAA2B,CAAC,mBAAmB,EAAE,sCAAsC,CAAC,CAAC;AAErH,MAAM,WAAW,wCAAwC;IACxD;;OAEG;IACH,gBAAgB,CAAC,EAAE,0BAA0B,CAAC;IAC9C;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,YAAY,EAAE,CAAC;IACvB;;OAEG;IACH,KAAK,CAAC,EAAE,QAAQ,CAAC;CACjB;AAED,MAAM,MAAM,oCAAoC,GAAG,cAAc,CAAC;AAElE,MAAM,MAAM,sCAAsC,GAAG,KAAK,CAAC;AAE3D,UAAU,mBAAmB,CAAC,IAAI,SAAS,sBAAsB,EAAE,KAAK;IACvE;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC;IACX;;OAEG;IACH,cAAc,EAAE,SAAS,CAAC;IAC1B;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IACX;;OAEG;IACH,KAAK,EAAE,KAAK,CAAC;CACb;AAED;;GAEG;AACH,oBAAY,sBAAsB;IACjC;;OAEG;IACH,IAAI,IAAA;IACJ;;OAEG;IACH,KAAK,IAAA;CACL;AAED,UAAU,wBAAwB,CAAC,IAAI,SAAS,2BAA2B,EAAE,IAAI;IAChF;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IACX;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;CACX;AAED;;GAEG;AACH,oBAAY,2BAA2B;IACtC;;OAEG;IACH,qBAAqB,2BAA2B;IAChD;;OAEG;IACH,iBAAiB,uBAAuB;IACxC;;OAEG;IACH,mBAAmB,0BAA0B;CAC7C;AAED;;GAEG;AACH,oBAAY,WAAW;IACtB;;OAEG;IACH,QAAQ,IAAI;IACZ;;OAEG;IACH,eAAe,IAAA;IACf;;OAEG;IACH,WAAW,IAAA;CACX"}