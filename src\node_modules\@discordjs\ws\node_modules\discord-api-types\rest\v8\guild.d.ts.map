{"version": 3, "file": "guild.d.ts", "sourceRoot": "", "sources": ["guild.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EACX,MAAM,EACN,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,2BAA2B,EAC3B,eAAe,EACf,qBAAqB,EACrB,cAAc,EACd,sBAAsB,EACtB,OAAO,EACP,cAAc,EACd,gCAAgC,EAChC,0BAA0B,EAC1B,YAAY,EACZ,uBAAuB,EACvB,sBAAsB,EACtB,gBAAgB,EAChB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC1G,OAAO,KAAK,EAAE,mCAAmC,EAAE,MAAM,WAAW,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,mCAAmC;IACnF,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,OAAO,CAAC,UAAU,EAAE,YAAY,GAAG,iBAAiB,CAAC,CAAC;AAE9F;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,aAAa,CACvD,IAAI,CACH,mBAAmB,CAAC,yBAAyB,CAAC,EAC9C,SAAS,GAAG,MAAM,GAAG,qBAAqB,GAAG,OAAO,GAAG,MAAM,GAAG,YAAY,CAC5E,CACD,GAAG;IACH,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACjC,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC/C,qBAAqB,CAAC,EAAE,uBAAuB,EAAE,GAAG,SAAS,CAAC;CAC9D,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,4BAA4B;IACvE,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,yBAAyB;IACzC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC5B;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;IACxD;;;;OAIG;IACH,6BAA6B,CAAC,EAAE,gCAAgC,GAAG,SAAS,CAAC;IAC7E;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,0BAA0B,GAAG,SAAS,CAAC;IACjE;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,EAAE,kBAAkB,EAAE,GAAG,SAAS,CAAC;IACzC;;;;;;;;;;;;OAYG;IACH,QAAQ,CAAC,EAAE,4BAA4B,EAAE,GAAG,SAAS,CAAC;IACtD;;OAEG;IACH,cAAc,CAAC,EAAE,SAAS,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvD;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,iBAAiB,CAAC,EAAE,SAAS,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1D;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IAC3D;;OAEG;IACH,4BAA4B,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACnD;AAED;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACpC;;;;OAIG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,MAAM,qBAAqB,GAAG,QAAQ,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,eAAe,CAAC;AAE3D;;;;GAIG;AACH,MAAM,WAAW,yBAAyB;IACzC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,sBAAsB,GAAG,IAAI,GAAG,SAAS,CAAC;IAC/D;;;;OAIG;IACH,6BAA6B,CAAC,EAAE,gCAAgC,GAAG,IAAI,GAAG,SAAS,CAAC;IACpF;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,0BAA0B,GAAG,IAAI,GAAG,SAAS,CAAC;IACxE;;OAEG;IACH,cAAc,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9C;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IACjC;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,iBAAiB,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IACjD;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IAC3D;;OAEG;IACH,gBAAgB,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD;;OAEG;IACH,yBAAyB,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C;;;;OAIG;IACH,QAAQ,CAAC,EAAE,YAAY,EAAE,GAAG,SAAS,CAAC;IACtC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC;;OAEG;IACH,4BAA4B,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACnD;AAED;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,UAAU,EAAE,CAAC;AAEzD;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;AAEvF;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,UAAU,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,yCAAyC,GAAG;IACvD;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACvC;;OAEG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;CACzC,EAAE,CAAC;AAEJ;;;;GAIG;AACH,MAAM,MAAM,uCAAuC,GAAG,KAAK,CAAC;AAE5D;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,cAAc,CAAC;AAEzD;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;CAClB;AAED;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,cAAc,EAAE,CAAC;AAE5D;;;;GAIG;AACH,MAAM,WAAW,iCAAiC;IACjD;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,cAAc,EAAE,CAAC;AAElE;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;IAChC;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC3B;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG,cAAc,GAAG,SAAS,CAAC;AAErE;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;;;OAIG;IACH,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,UAAU,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1C;;OAEG;IACH,4BAA4B,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACzD;AAED;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,cAAc,CAAC;AAE3D;;;;;GAKG;AACH,MAAM,WAAW,8CAA8C;IAC9D;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACjC;AAED;;;;GAIG;AACH,MAAM,WAAW,sCAAsC;IACtD;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACjC;AAED;;;;;GAKG;AACH,MAAM,MAAM,4CAA4C,GACvD,cAAc,CAAC,8CAA8C,CAAC,CAAC;AAEhE;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,KAAK,CAAC;AAEpD;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,MAAM,EAAE,CAAC;AAEjD;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,MAAM,CAAC;AAE9C;;;;GAIG;AACH,MAAM,WAAW,0BAA0B;IAC1C;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACzC;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,KAAK,CAAC;AAEhD;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,OAAO,EAAE,CAAC;AAEnD;;;;GAIG;AACH,MAAM,WAAW,4BAA4B;IAC5C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;;;OAIG;IACH,WAAW,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1C;;;;OAIG;IACH,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACzC;AAED;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,OAAO,CAAC;AAEjD;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG;IACpD;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC9B,EAAE,CAAC;AAEJ;;;;GAIG;AACH,MAAM,MAAM,oCAAoC,GAAG,OAAO,EAAE,CAAC;AAE7D;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1C;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACzC;AAED;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,OAAO,CAAC;AAElD;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,KAAK,CAAC;AAEjD;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;;;;OAOG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACvB;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C,MAAM,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1C;;OAEG;IACH,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;CACxC;AAED;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC3C,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,cAAc,EAAE,CAAC;AAEjE;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,iBAAiB,EAAE,CAAC;AAE/D;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,mBAAmB,EAAE,CAAC;AAEtE;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,KAAK,CAAC;AAExD;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,sBAAsB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,MAAM,uCAAuC,GAAG,aAAa,CAAC,sBAAsB,CAAC,CAAC;AAE5F;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAAG,sBAAsB,CAAC;AAE3E;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,cAAc,CAAC;AAE7D;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;CACb;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C;;;;OAIG;IACH,KAAK,CAAC,EAAE,gBAAgB,CAAC;CACzB;AAED;;;;;GAKG;AACH,MAAM,MAAM,gCAAgC,GAAG,WAAW,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,2BAA2B,CAAC;AAElF;;GAEG;AACH,MAAM,WAAW,2CAA2C;IAC3D;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC9B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,MAAM,yCAAyC,GAAG,2BAA2B,CAAC;AAEpF;;GAEG;AACH,MAAM,WAAW,gDAAgD;IAChE;;OAEG;IACH,UAAU,EAAE,SAAS,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC/B;;OAEG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACvD;AAED;;GAEG;AACH,MAAM,WAAW,uCAAuC;IACvD;;OAEG;IACH,UAAU,EAAE,SAAS,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAC/B;AAED;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAAG,qBAAqB,CAAC;AAEvE;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,GAAG;IACrG;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACrC,CAAC"}