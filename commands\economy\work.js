const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { addMoney, getBalance } = require('../../utils/economy');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('work')
        .setDescription('Work to earn money'),

    async execute(interaction) {
        const cooldowns = global.cooldowns || new Map();
        const cooldownTime = 3600000; // 1 hour in milliseconds

        if (cooldowns.has(`work_${interaction.user.id}`)) {
            const expirationTime = cooldowns.get(`work_${interaction.user.id}`);
            const timeLeft = (expirationTime - Date.now()) / 1000 / 60; // Convert to minutes
            
            return interaction.reply({
                content: `You need to wait ${timeLeft.toFixed(1)} minutes before working again!`,
                ephemeral: true
            });
        }

        const earnings = Math.floor(Math.random() * (5000 - 1000 + 1)) + 1000;
        await addMoney(interaction.user.id, earnings, 'wallet');

        const jobs = [
            'as a software developer',
            'as a construction worker',
            'as a teacher',
            'as a chef',
            'as a police officer',
            'as a firefighter',
            'as a doctor',
            'as a lawyer'
        ];

        const job = jobs[Math.floor(Math.random() * jobs.length)];
        const embed = new EmbedBuilder()
            .setColor('#2B2D31')
            .setTitle('Work Complete!')
            .setDescription(`You worked ${job} and earned $${earnings.toLocaleString()}!`)
            .setFooter({ text: 'Southwest Florida Roleplay Realm' });

        cooldowns.set(`work_${interaction.user.id}`, Date.now() + cooldownTime);
        setTimeout(() => cooldowns.delete(`work_${interaction.user.id}`), cooldownTime);

        await interaction.reply({ embeds: [embed] });
    },
};