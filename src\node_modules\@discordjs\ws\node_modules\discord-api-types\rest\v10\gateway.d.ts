import type { APIGatewayBotInfo, APIGatewayInfo } from '@discordjs/ws/node_modules/discord-api-types/payloads/v10';
/**
 * https://discord.com/developers/docs/topics/gateway#get-gateway
 */
export type RESTGetAPIGatewayResult = APIGatewayInfo;
/**
 * https://discord.com/developers/docs/topics/gateway#get-gateway-bot
 */
export type RESTGetAPIGatewayBotResult = APIGatewayBotInfo;
//# sourceMappingURL=gateway.d.ts.map