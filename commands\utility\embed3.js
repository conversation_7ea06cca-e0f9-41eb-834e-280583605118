
const { <PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed3')
    .setDescription('Displays server information and rules'),
  async execute(interaction) {
    const embedColor = '#2B2D31';
    const imageURL = "https://cdn.discordapp.com/attachments/1304908359262277673/1307472783155925013/Copy_of_Copy_of_j_5.png?ex=678c2b6b&is=678ad9eb&hm=afde20a63d045b3e2f7eda68bb03eee4f2914bbe3c46eb353abf8ab5230a3dd4&";

    // Defer reply and edit later
    await interaction.deferReply({ ephemeral: false });

    // Reply with image
    await interaction.followUp({ files: [imageURL] });

    const embeds = [
      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Staff Information & Guidelines')
        .setDescription(
          'Welcome to the staff team of Southwest Florida Roleplay Realm In this channel, you will find all the essential information you need while working as staff here at SFRR.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 1: Leaking information')
        .setDescription(
          'If you are caught leaking information inaccessible to the community members, such as staff channels, documents, forms, etc… You will face termination and a high command’s staff blacklist. If you resign or are terminated and still leak information, you will be banned and permanently blacklisted from the staff team.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 2: Power Abuse')
        .setDescription(
          'Abuse of power is highly prohibited within the Southwest Florida Roleplay Realm Staff team. Anyone caught abusing power will be given a staff strike by the high command team.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 3: Grammar')
        .setDescription(
          'As a member of the Southwest Florida Roleplay Realm staff team, it is important to maintain proper grammar in professional situations, This includes startup channels, In-game announcements, and when interacting with civilians. However, it is not necessary to adhere to strict grammar rules in chat rooms.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 4: Absence Policies')
        .setDescription(
          'It is highly prohibited to exceed a to-week absence period and you are authorized to take only one leave of absence per month. If you submit an absence notice to void your weekly quota disciplinary measures will be taken against you.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 5: Professionalism & Drama')
        .setDescription(
          'As a member of Southwest Florida Roleplay Realm, it is essential to uphold professionalism not only within our server but also in other servers. Additionally, it is highly prohibited to engage in any form of drama. Any individual found responsible for creating or participating in drama will face strict actions.'
        ),

        new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 6: Activity')
        .setDescription(
          'You must maintain constant activity within Southwest Florida Roleplay Realm. This entails not only hosting sessions but also actively participating in the server’s chatroom and forming new connections with fellow members.'
        ),

        new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 7: Startup Channels')
        .setDescription(
          'When hosting in the startup channels, It is imperative to refrain from engaging in side conversations as it reflects unprofessional behavior and creates a disorganized atmosphere. Additionally, startup channels must be cleared once your session has concluded. Failure to comply with these guidelines may result in strict measures being taken against you.'
        ),

        new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 8: Moderating Users')
        .setDescription(
          'When moderating users it is required to provide acceptable evidence. Failure to provide valid evidence will result in the nullification of the user’s moderation.'
        ),

        new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 9: Reckless Driving')
        .setDescription(
          'When conducting patrols in your staff vehicle, you must adhere to the staff driving regulations as any other civilian. Failure to roleplay properly while operating your staff vehicle, such as driving in the wrong direction or exceeding speed limits will result in strict measures being taken against you.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Realm' }),
    ];

    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('embed3-menu')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Session Commands',
            description: '...',
            value: 'session-commands',
          },
          {
            label: 'Staff Quota',
            description: '...',
            value: 'staff-quota',
          },
        ])
    );

    await interaction.editReply({ embeds, components: [menu] });
  },
};
