const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('tictactoe')
        .setDescription('Start a game of Tic Tac Toe')
        .addUserOption(option => 
            option.setName('opponent')
                .setDescription('The user you want to play against')
                .setRequired(true)),

    async execute(interaction) {
        const opponent = interaction.options.getUser('opponent');
        
        if (opponent.bot) {
            return interaction.reply({ content: 'You cannot play against a bot!', ephemeral: true });
        }
        
        if (opponent.id === interaction.user.id) {
            return interaction.reply({ content: 'You cannot play against yourself!', ephemeral: true });
        }

        const gameId = `${interaction.user.id}-${opponent.id}-${Date.now()}`;
        const gameData = {
            players: [interaction.user.id, opponent.id],
            currentTurn: interaction.user.id,
            board: Array(9).fill(null),
            gameOver: false
        };

        if (!global.tictactoeGames) {
            global.tictactoeGames = new Map();
        }
        global.tictactoeGames.set(gameId, gameData);

        // Create the game board with buttons
        const rows = [];
        for (let i = 0; i < 3; i++) {
            const actionRow = new ActionRowBuilder();
            const buttons = [];
            
            for (let j = 0; j < 3; j++) {
                const index = i * 3 + j;
                const button = new ButtonBuilder()
                    .setCustomId(`ttt_${gameId}_${index}`)
                    .setLabel('\u200b')  // Using zero-width space as label
                    .setStyle(ButtonStyle.Secondary);
                    
                buttons.push(button);
            }
            
            actionRow.addComponents(buttons);
            rows.push(actionRow);
        }

        const embed = new EmbedBuilder()
            .setTitle('Tic Tac Toe')
            .setDescription(`${interaction.user} (X) vs ${opponent} (O)\nCurrent turn: ${interaction.user}`)
            .setColor('#2B2D31');

        await interaction.reply({
            embeds: [embed],
            components: rows
        });
    },
};
