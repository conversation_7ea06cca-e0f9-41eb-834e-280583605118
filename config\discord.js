const passport = require('passport');
const DiscordStrategy = require('passport-discord').Strategy;

const scopes = ['identify', 'guilds'];

passport.serializeUser((user, done) => done(null, user));
passport.deserializeUser((user, done) => done(null, user));

passport.use(new DiscordStrategy({
    clientID: process.env.DISCORD_CLIENT_ID,
    clientSecret: process.env.DISCORD_CLIENT_SECRET,
    callbackURL: process.env.DISCORD_CALLBACK_URL,
    scope: scopes
}, async (accessToken, refreshToken, profile, done) => {
    try {
        // You can store user data in your database here
        return done(null, profile);
    } catch (error) {
        return done(error, null);
    }
}));

module.exports = passport;