const { SlashCommandBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('settingup')
    .setDescription('Notifies that staff, boosters, emergency services, and content creators can join.'),

  async execute(interaction) {
    const staffRoleId = '1279933324298817608'; // Replace with your actual staff role ID
    const logChannelId = '1279642823951646760'; // Replace with your log channel ID

    if (!interaction.member.roles.cache.has(staffRoleId)) {
      await interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
      return;
    }

    const settingUpMessage = 'Setting up. Staff, Boosters, Emergency Services & Content Creators may now join!';

    // Acknowledge the interaction and respond with an ephemeral message
    await interaction.reply({ content: 'Setting Message Released!', ephemeral: true });

    // Send the message publicly
    await interaction.channel.send(settingUpMessage);

    // Log the command execution as a plain message instead of an embed
    const logChannel = interaction.guild.channels.cache.get(logChannelId);
    if (logChannel) {
      const logMessage = `The \`/settingup\` command was executed by ${interaction.user.tag} in #${interaction.channel.name}.`;
      
      logChannel.send(logMessage);
    }
  }
};
