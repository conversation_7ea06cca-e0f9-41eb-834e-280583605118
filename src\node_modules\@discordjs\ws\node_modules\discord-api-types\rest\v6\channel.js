"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllowedMentionsTypes = void 0;
/**
 * https://discord.com/developers/docs/resources/channel#allowed-mentions-object-allowed-mention-types
 *
 * @deprecated API v6 is deprecated and the types will not receive further updates, please update to v8.
 */
var AllowedMentionsTypes;
(function (AllowedMentionsTypes) {
    AllowedMentionsTypes["Everyone"] = "everyone";
    AllowedMentionsTypes["Role"] = "roles";
    AllowedMentionsTypes["User"] = "users";
})(AllowedMentionsTypes || (exports.AllowedMentionsTypes = AllowedMentionsTypes = {}));
//# sourceMappingURL=channel.js.map