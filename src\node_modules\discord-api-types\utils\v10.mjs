import mod from "./v10.js";

export default mod;
export const isApplicationCommandDMInteraction = mod.isApplicationCommandDMInteraction;
export const isApplicationCommandGuildInteraction = mod.isApplicationCommandGuildInteraction;
export const isChatInputApplicationCommandInteraction = mod.isChatInputApplicationCommandInteraction;
export const isContextMenuApplicationCommandInteraction = mod.isContextMenuApplicationCommandInteraction;
export const isDMInteraction = mod.isDMInteraction;
export const isGuildInteraction = mod.isGuildInteraction;
export const isInteractionButton = mod.isInteractionButton;
export const isLinkButton = mod.isLinkButton;
export const isMessageComponentButtonInteraction = mod.isMessageComponentButtonInteraction;
export const isMessageComponentDMInteraction = mod.isMessageComponentDMInteraction;
export const isMessageComponentGuildInteraction = mod.isMessageComponentGuildInteraction;
export const isMessageComponentInteraction = mod.isMessageComponentInteraction;
export const isMessageComponentSelectMenuInteraction = mod.isMessageComponentSelectMenuInteraction;
