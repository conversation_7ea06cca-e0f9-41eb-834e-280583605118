const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed2')
    .setDescription('Displays server information and rules'),
  async execute(interaction) {
    const embedColor = '#2B2D31';
    const imageURL = "https://cdn.discordapp.com/attachments/1304908359262277673/1307472108518903838/Copy_of_Copy_of_j_1.png?ex=678ad94a&is=678987ca&hm=f03fcda937f7c8bec302811cf7b32d907a4867b63c6635ae25026288c7520c6e&";

    // Check if the user has permission to use this command
    const staffRoleId = '1237979199747129396';
    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    // Defer reply and edit later
    await interaction.deferReply({ ephemeral: false });

    // Reply with image
    await interaction.followUp({ files: [imageURL] });

    const embeds = [
      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Server Information')
        .setDescription(
          'Within this channel, you can find all the necessary information for our sessions and server within this channel. Such as peacetime, rules, session information, and more.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 1: Promotion')
        .setDescription(
          'Promoting your server within our general community is highly prohibited. If you are caught doing so you will be banned from this server.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 2: NSFW')
        .setDescription(
          'Sending any NSFW in our general channels is highly prohibited. If you are caught doing this you will be banned from our server permanently.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 3: Drama')
        .setDescription(
          'Starting any sort of drama is prohibited. If you are caught you will face consequences.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 4: Respect')
        .setDescription(
          'Respect all civilians, staff, and anyone else on this server. If you are caught disrespecting any of our staff or civilians, you will face consequences.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 5: Racism')
        .setDescription(
          'Racism is strictly prohibited within SFRR. If you are caught being racist you will face consequences.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Realm' }),
    ];

    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('embed2-menu')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Roleplay Information',
            description: 'View roleplay-related rules and guidelines',
            value: 'roleplay-info',
          },
          {
            label: 'Banned Vehicle List',
            description: 'View the list of banned vehicles',
            value: 'banned-vehicles',
          },
        ])
    );

    // Log the command execution
    try {
      const logChannelId = '1279642823951646760';
      const logChannel = interaction.guild.channels.cache.get(logChannelId);
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setTitle('Command Executed')
          .setDescription('The `/embed2` command was executed.')
          .addFields(
            { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
            { name: 'User ID', value: `${interaction.user.id}`, inline: true },
            { name: 'Channel', value: `${interaction.channel.name}`, inline: true }
          )
          .setColor('#2B2D31')
          .setTimestamp();

        logChannel.send({ embeds: [logEmbed] });
      }
    } catch (error) {
      console.error('Error logging command execution:', error);
    }

    await interaction.editReply({ embeds, components: [menu] });
  },
};
