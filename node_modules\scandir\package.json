{"name": "scandir", "version": "0.1.2", "description": "recursive directory scan with filtering features", "main": "lib/scandir.js", "bin": {"scandir": "./bin/scandir"}, "directories": {"test": "test"}, "scripts": {"pretest": "jsrevival -p \"describe, it\" -o \"properties: false, maxlen: 0, debug: false, bitwise: false, eqeq: false, newcap: false, unparam: false, vars: false, passfail: false\" -r cli-hide-valid -R lib/ test/", "test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/sdolard/node-scandir.git"}, "keywords": ["readdir", "recursive"], "author": "sdo<PERSON>@gmail.com", "license": "BSD", "readmeFilename": "README.md", "gitHead": "93b6c7035fcffaf7cd3473aebb7eaba278bc68f5", "engines": {"node": ">=0.8.0"}, "dependencies": {"commander": "2.x.x", "mime": "1.2.x", "ansi": "0.2.x"}, "devDependencies": {"jsrevival": "0.2.x", "mocha": "x.x.x"}}