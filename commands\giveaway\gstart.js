const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

const GIVEAWAYS_FILE = path.join(__dirname, '../../data/giveaways.json');

// Ensure data directory exists
const dataDir = path.dirname(GIVEAWAYS_FILE);
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Load giveaways from file
function loadGiveaways() {
    try {
        if (fs.existsSync(GIVEAWAYS_FILE)) {
            return JSON.parse(fs.readFileSync(GIVEAWAYS_FILE, 'utf8'));
        }
    } catch (error) {
        console.error('Error loading giveaways:', error);
    }
    return {};
}

// Save giveaways to file
function saveGiveaways(giveaways) {
    try {
        fs.writeFileSync(GIVEAWAYS_FILE, JSON.stringify(giveaways, null, 2));
    } catch (error) {
        console.error('Error saving giveaways:', error);
    }
}

// Parse duration string (e.g., "1h", "30m", "2d")
function parseDuration(duration) {
    const regex = /^(\d+)([smhd])$/;
    const match = duration.match(regex);
    
    if (!match) return null;
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    const multipliers = {
        's': 1000,
        'm': 60 * 1000,
        'h': 60 * 60 * 1000,
        'd': 24 * 60 * 60 * 1000
    };
    
    return value * multipliers[unit];
}

// Format duration for display
function formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('giveawaystart')
        .setDescription('Start a new giveaway')
        .addStringOption(option =>
            option.setName('duration')
                .setDescription('Duration of the giveaway (e.g., 1h, 30m, 2d)')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('winners')
                .setDescription('Number of winners')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(20))
        .addStringOption(option =>
            option.setName('prize')
                .setDescription('What is being given away')
                .setRequired(true))
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('Channel to host the giveaway in (default: current channel)')
                .setRequired(false))
        .addRoleOption(option =>
            option.setName('required_role')
                .setDescription('Role required to enter the giveaway')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('description')
                .setDescription('Additional description for the giveaway')
                .setRequired(false)),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has('ManageMessages')) {
            return await interaction.reply({
                content: 'You need the `Manage Messages` permission to start giveaways.',
                ephemeral: true
            });
        }

        const duration = interaction.options.getString('duration');
        const winners = interaction.options.getInteger('winners');
        const prize = interaction.options.getString('prize');
        const channel = interaction.options.getChannel('channel') || interaction.channel;
        const requiredRole = interaction.options.getRole('required_role');
        const description = interaction.options.getString('description');

        // Parse duration
        const durationMs = parseDuration(duration);
        if (!durationMs) {
            return await interaction.reply({
                content: 'Invalid duration format. Use formats like: `1h`, `30m`, `2d`, `45s`',
                ephemeral: true
            });
        }

        // Check if duration is reasonable
        if (durationMs < 10000) { // Less than 10 seconds
            return await interaction.reply({
                content: 'Giveaway duration must be at least 10 seconds.',
                ephemeral: true
            });
        }

        if (durationMs > 30 * 24 * 60 * 60 * 1000) { // More than 30 days
            return await interaction.reply({
                content: 'Giveaway duration cannot exceed 30 days.',
                ephemeral: true
            });
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const endTime = Date.now() + durationMs;
            const giveawayId = `giveaway_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Create giveaway embed
            const embed = new EmbedBuilder()
                .setTitle('🎉 GIVEAWAY 🎉')
                .setDescription(`**Prize:** ${prize}\n\n${description || 'React with 🎉 to enter!'}\n\n**Winners:** ${winners}\n**Ends:** <t:${Math.floor(endTime / 1000)}:R>\n**Hosted by:** ${interaction.user}`)
                .setColor('#FF6B6B')
                .setFooter({ text: `${winners} winner${winners > 1 ? 's' : ''} | Ends at` })
                .setTimestamp(endTime);

            if (requiredRole) {
                embed.addFields({ name: '📋 Requirements', value: `Must have the ${requiredRole} role`, inline: false });
            }

            // Create button
            const button = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`giveaway_enter_${giveawayId}`)
                        .setLabel('🎉 Enter Giveaway')
                        .setStyle(ButtonStyle.Primary)
                );

            // Send giveaway message
            const giveawayMessage = await channel.send({
                embeds: [embed],
                components: [button]
            });

            // Save giveaway data
            const giveaways = loadGiveaways();
            giveaways[giveawayId] = {
                messageId: giveawayMessage.id,
                channelId: channel.id,
                guildId: interaction.guild.id,
                hostId: interaction.user.id,
                prize: prize,
                winners: winners,
                endTime: endTime,
                requiredRole: requiredRole?.id || null,
                description: description,
                entries: [],
                ended: false,
                createdAt: Date.now()
            };
            saveGiveaways(giveaways);

            await interaction.editReply({
                content: `Giveaway started in ${channel}!\n🎉 **Prize:** ${prize}\n⏰ **Duration:** ${formatDuration(durationMs)}\n🏆 **Winners:** ${winners}`
            });

            // Set timeout to end giveaway
            setTimeout(async () => {
                await endGiveaway(giveawayId, interaction.client);
            }, durationMs);

        } catch (error) {
            console.error('Error starting giveaway:', error);
            await interaction.editReply({
                content: 'An error occurred while starting the giveaway. Please try again.'
            });
        }
    }
};

// Function to end giveaway (will be used by timeout and manual end)
async function endGiveaway(giveawayId) {
    const giveaways = loadGiveaways();
    const giveaway = giveaways[giveawayId];
    
    if (!giveaway || giveaway.ended) return;
    
    try {
        const { Client } = require('discord.js');
        const client = require('../../index.js'); // Adjust path as needed
        
        const guild = await client.guilds.fetch(giveaway.guildId);
        const channel = await guild.channels.fetch(giveaway.channelId);
        const message = await channel.messages.fetch(giveaway.messageId);
        
        // Mark as ended
        giveaway.ended = true;
        giveaways[giveawayId] = giveaway;
        saveGiveaways(giveaways);
        
        // Select winners
        const entries = giveaway.entries;
        if (entries.length === 0) {
            // No entries
            const embed = new EmbedBuilder()
                .setTitle('🎉 GIVEAWAY ENDED 🎉')
                .setDescription(`**Prize:** ${giveaway.prize}\n\n No valid entries! No winners selected.`)
                .setColor('#FF0000')
                .setFooter({ text: 'Giveaway ended' })
                .setTimestamp();
                
            await message.edit({ embeds: [embed], components: [] });
            return;
        }
        
        // Select random winners
        const shuffled = [...entries].sort(() => 0.5 - Math.random());
        const winners = shuffled.slice(0, Math.min(giveaway.winners, entries.length));
        
        // Update embed
        const embed = new EmbedBuilder()
            .setTitle('🎉 GIVEAWAY ENDED 🎉')
            .setDescription(`**Prize:** ${giveaway.prize}\n\n🏆 **Winner${winners.length > 1 ? 's' : ''}:** ${winners.map(id => `<@${id}>`).join(', ')}\n\n**Hosted by:** <@${giveaway.hostId}>`)
            .setColor('#00FF00')
            .setFooter({ text: 'Giveaway ended' })
            .setTimestamp();
            
        await message.edit({ embeds: [embed], components: [] });
        
        // Announce winners
        await channel.send(`🎉 Congratulations ${winners.map(id => `<@${id}>`).join(', ')}! You won **${giveaway.prize}**!`);
        
    } catch (error) {
        console.error('Error ending giveaway:', error);
    }
}

// Export the endGiveaway function for use in other files
module.exports.endGiveaway = endGiveaway;
