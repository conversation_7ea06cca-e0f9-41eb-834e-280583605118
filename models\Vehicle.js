const mongoose = require('mongoose');

const vehicleSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    vehicles: [{
        year: { type: Number, required: true },
        make: { type: String, required: true },
        model: { type: String, required: true },
        color: { type: String, required: true },
        numberPlate: { 
            type: String, 
            required: true,
            uppercase: true
        },
        createdAt: { 
            type: Date, 
            default: Date.now 
        }
    }]
});

// Check if model exists before creating
module.exports = mongoose.models.Vehicle || mongoose.model('Vehicle', vehicleSchema);



