const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder } = require('discord.js');

// Variable to store bot startup time
const botStartTime = Date.now();

// Function to calculate uptime
function getBotUptime() {
    const currentTime = Date.now();
    const uptime = currentTime - botStartTime;

    const seconds = Math.floor((uptime / 1000) % 60);
    const minutes = Math.floor((uptime / (1000 * 60)) % 60);
    const hours = Math.floor((uptime / (1000 * 60 * 60)) % 24);
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));

    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('botinfo')
        .setDescription('This will display our botinfo'),
    async execute(interaction) {
        // Create the embed message
        const embed = new EmbedBuilder()
            .setTitle('Bot Information')
            .setColor('#2B2D31') // Set the color here
            .addFields(
                { name: 'Bot Name', value: 'Southwest Florida Roleplay Realm Test Bot', inline: true },
                { name: 'Developer', value: '<@881721027510550528>', inline: true },
                { name: 'Node Version', value: 'V20', inline: true },
                { name: 'Programming Language', value: 'Javascript', inline: true },
                { name: 'Uptime', value: getBotUptime(), inline: true }
            )
            .setFooter({
                text: 'Southwest Florida Roleplay Realm', 
                iconURL: 'https://cdn.discordapp.com/attachments/1282761548246552596/1291855078399148073/Screenshot_2024-09-14_150918.png?ex=6702eecc&is=67019d4c&hm=528d6dc8c462bb7d5be7fa3c05b0da9d2696d67784ef5fd9ab4cac99e839a62b'
            });

        // Reply with the embed
        await interaction.reply({ embeds: [embed] });
    }
};
