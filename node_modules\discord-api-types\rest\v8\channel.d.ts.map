{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EACX,qBAAqB,EACrB,kBAAkB,EAClB,aAAa,EACb,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,4BAA4B,EAC5B,mBAAmB,EACnB,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,oDAAoD,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEjH;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,mCAAmC;IACpF,EAAE,EAAE,SAAS,CAAC;CACd;AAED;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B;;;;;OAKG;IACH,IAAI,CAAC,EAAE,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;IACjE;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,wBAAwB,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACtE;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,GAAG,IAAI,GAAG,SAAS,CAAC;IACzC;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAAC;CACzD;AAED;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,MAAM,CAAC,EAAE,SAAS,CAAC;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,UAAU,CAAC;AAExD;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GAAG,oDAAoD,CACzF,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC,CACjD,GACA,aAAa,CAAC,mBAAmB,CAAC,GAAG;IACpC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACzC,CAAC;AAEH;;;;GAIG;AACH,MAAM,WAAW,iCAAiC;IACjD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1B;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IAChC;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAClD;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IACxD;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,SAAS,CAAC;IAC/E;;;;OAIG;IACH,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;IACnG;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IACnH;;OAEG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;CACjC;AAED;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAC9C,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,iCAAiC,CAAC,CAAC;AAE7E;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;;;GAIG;AACH,MAAM,MAAM,wCAAwC,GAAG,UAAU,CAAC;AAElE;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;;;GAIG;AACH,MAAM,MAAM,6CAA6C,GAAG,KAAK,CAAC;AAElE;;;;GAIG;AACH,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,MAAM,2CAA2C,GAAG,OAAO,EAAE,CAAC;AAEpE;;;;GAIG;AACH,MAAM,MAAM,6CAA6C,GAAG,KAAK,CAAC;AAElE;;;;GAIG;AACH,MAAM,MAAM,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;;;GAIG;AACH,MAAM,WAAW,kCAAkC;IAClD;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;OAIG;IACH,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,SAAS,CAAC;IACxC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,GAAG,IAAI,GAAG,SAAS,CAAC;IACzD;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC;IACnH;;;;OAIG;IACH,UAAU,CAAC,EAAE,qBAAqB,CAAC,4BAA4B,CAAC,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;CACtF;AAED;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAC/C,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,kCAAkC,CAAC,CAAC;AAE9E;;;;GAIG;AACH,MAAM,MAAM,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;;GAIG;AACH,MAAM,WAAW,4CAA4C;IAC5D;;OAEG;IACH,QAAQ,EAAE,SAAS,EAAE,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,MAAM,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;;;GAIG;AACH,MAAM,WAAW,mCAAmC;IACnD;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC;IACtC;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,iBAAiB,EAAE,CAAC;AAEjE;;;;GAIG;AACH,MAAM,WAAW,gCAAgC;IAChD;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B;;;;OAIG;IACH,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B;;;;OAIG;IACH,WAAW,CAAC,EAAE,gBAAgB,GAAG,SAAS,CAAC;IAC3C;;;;OAIG;IACH,cAAc,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IACvC;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;CAC9C;AAED;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,iBAAiB,CAAC;AAE/D;;;;GAIG;AACH,MAAM,MAAM,oCAAoC,GAAG,KAAK,CAAC;AAEzD;;;;GAIG;AACH,MAAM,WAAW,mCAAmC;IACnD;;OAEG;IACH,kBAAkB,EAAE,SAAS,CAAC;CAC9B;AAED;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,kBAAkB,CAAC;AAEnE;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;;GAIG;AACH,MAAM,WAAW,kCAAkC;IAClD;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC1B;AAED;;;;GAIG;AACH,MAAM,MAAM,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,OAAO,CAAC"}