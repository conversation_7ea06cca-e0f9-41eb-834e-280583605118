{"version": 3, "file": "oauth2.d.ts", "sourceRoot": "", "sources": ["oauth2.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAE3G;;;;GAIG;AACH,MAAM,MAAM,wCAAwC,GAAG,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAErF;;;;GAIG;AACH,MAAM,WAAW,0CAA0C;IAC1D;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACrC;;OAEG;IACH,MAAM,EAAE,YAAY,EAAE,CAAC;IACvB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,WAAW,4BAA4B;IAC5C,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,SAAS,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,WAAW,kCAAkC;IAClD,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,WAAW,uCAAuC;IACvD,SAAS,EAAE,SAAS,CAAC;IACrB,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,oBAAoB,CAAC;IACjC,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;CACd;AAED;;;;GAIG;AACH,MAAM,WAAW,wCAAwC;IACxD,SAAS,EAAE,SAAS,CAAC;IACrB,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,eAAe,CAAC;IAC5B,aAAa,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,+BAA+B,CAAC;AAE/E;;;;GAIG;AACH,MAAM,WAAW,oCAAoC;IACpD,aAAa,EAAE,OAAO,CAAC;IACvB,SAAS,EAAE,SAAS,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,MAAM,gDAAgD,GAAG,IAAI,CAAC,+BAA+B,EAAE,eAAe,CAAC,CAAC;AAEtH;;;;GAIG;AACH,MAAM,WAAW,6CAA6C;IAC7D,UAAU,EAAE,oBAAoB,CAAC;IACjC,KAAK,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG,gDAAgD,CAAC;AAErG;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,KAAK,EACF,YAAY,CAAC,GAAG,GAChB,GAAG,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,MAAM,EAAE,GAC5C,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,CAAC,GAAG,EAAE,GAC5C,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,CAAC;IACvE;;;;OAIG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;;;GAIG;AACH,MAAM,WAAW,uCAAuC;IACvD,SAAS,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,KAAK,EACF,YAAY,CAAC,GAAG,GAChB,GAAG,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,MAAM,EAAE,GAC5C,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,CAAC,GAAG,EAAE,GAC5C,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,CAAC;IACvE;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC/B,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,6CAA6C;IAC7D,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,SAAS,CAAC;IACpB,WAAW,EAAE,WAAW,CAAC;CACzB;AAED;;;;GAIG;AACH,MAAM,WAAW,oDAAoD;IACpE,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,QAAQ,CAAC;CAChB;AAED;;;;GAIG;AACH,MAAM,WAAW,6DAA6D;IAC7E,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,UAAU,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,MAAM,sEAAsE,GACjF,oDAAoD,GACnD,6DAA6D,CAAC"}