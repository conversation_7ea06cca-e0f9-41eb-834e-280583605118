const { ActivityType } = require('discord.js');
const mongoose = require('mongoose');
const mongoURL = process.env.mongoURL;

module.exports = {
    name: 'ready',
    once: true,
    async execute(client) {
        console.log(`${client.user?.username} is online! (${client.user?.id})`);

        // Array of status objects
        const statuses = [
            {
                type: ActivityType.Watching,
                name: 'discord.gg/sfrr',
                status: 'online',
            },
        ];

        // Function to update bot's activity and status
        const updateStatus = () => {
            const { type, name, status } = statuses[0]; // Currently, only one status exists
            client.user.setPresence({
                activities: [{ name, type }],
                status,
            });
        };

        // Set initial activity
        updateStatus();

        // If you plan to add more statuses in the future, use a setInterval
        const rotationInterval = 10 * 1000; // 10 seconds
        setInterval(() => {
            updateStatus();
        }, rotationInterval);
    },
};
