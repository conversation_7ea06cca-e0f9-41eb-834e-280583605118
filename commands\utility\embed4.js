const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embed4')
        .setDescription('Send partnership requirements embed'),
    async execute(interaction) {
        const staffRoleId = '1277475295372513385'; // Replace with your actual staff role ID

        // Check if the user has the staff role
        if (!interaction.member.roles.cache.has(staffRoleId)) {
            await interaction.reply({ content: 'You do not have permission to use this command.', ephemeral: true });
            return;
        }

        // Define the embed
        const embed = new EmbedBuilder()
            .setTitle('Southwest Florida Roleplay Realm | Partnership Requirements')
            .setDescription(`> Welcome to our partnership channel where you can view our partnership requirements with our server! To partner with us, your server must meet the following criteria:\n\n- Must have 500+ members.\n
- Must be a professional server.\n
- Must have an active community.\n
- Must not have any negative history, such as raiding or NSFW content.\n\n
If your server meets these requirements, you can open a ticket to request a partnership. For servers with over 150 members:\n
- 300-400 members: We will ping @here.\n
- 500+ members: We will ping @everyone.\n
- Exactly 200 members: We will not ping.`)
            .setColor('#2B2D31')
            .setFooter({
                text: 'Southwest Florida Roleplay Realm',
                iconURL: 'https://cdn.discordapp.com/attachments/893617400321290311/1287887899551076445/Screenshot_2024-09-14_150918.png?ex=66f32e93&is=66f1dd13&hm=b4b9c6b0052461ab8c67620e28ff2c7ffdece8ed2ca7055760fe046b7a20e925&'
            })
            .setTimestamp();

        // Acknowledge the interaction and respond with an ephemeral message
        await interaction.reply({ content: 'Partnership requirements sent!', ephemeral: true });

        // Send the embed to the channel
        await interaction.channel.send({ embeds: [embed] });
    }
};
