const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const path = require('path');
const fs = require('fs');

const ticketsDirPath = path.join(__dirname, '../../data/tickets');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ticketremove')
        .setDescription('Remove a ticket from a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select a user')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('number')
                .setDescription('Ticket number to remove')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for removing the ticket')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const staffRoleId = '1279932257972260926';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.editReply({
                    content: 'You do not have permission to use this command.',
                    ephemeral: true
                });
            }

            const targetUser = interaction.options.getUser('user');
            const ticketNumber = interaction.options.getInteger('number');
            const reason = interaction.options.getString('reason');

            const ticketFilePath = path.join(ticketsDirPath, `${targetUser.id}.json`);
            
            if (!fs.existsSync(ticketFilePath)) {
                return await interaction.editReply({
                    content: 'This user has no tickets.',
                    ephemeral: true
                });
            }

            let tickets = JSON.parse(fs.readFileSync(ticketFilePath, 'utf8'));
            
            if (tickets.length === 0) {
                return await interaction.editReply({
                    content: 'This user has no tickets to remove.',
                    ephemeral: true
                });
            }

            if (ticketNumber < 1 || ticketNumber > tickets.length) {
                return await interaction.editReply({
                    content: `Invalid ticket number. Please enter a number between 1 and ${tickets.length}.`,
                    ephemeral: true
                });
            }

            // Remove the specific ticket (array index is ticket number - 1)
            const removedTicket = tickets.splice(ticketNumber - 1, 1)[0];

            // Save the updated tickets array or delete the file if empty
            if (tickets.length > 0) {
                fs.writeFileSync(ticketFilePath, JSON.stringify(tickets, null, 2));
            } else {
                fs.unlinkSync(ticketFilePath);
            }

            // DM the user
            try {
                const dmEmbed = new EmbedBuilder()
                    .setTitle('Ticket Removed')
                    .setDescription(`
                        **Original Offense:** ${removedTicket.offense}
                        **Amount:** $${removedTicket.price}
                        **Removal Reason:** ${reason}
                        **Removed By:** ${interaction.user.tag}
                    `)
                    .setColor('#00FF00')
                    .setTimestamp();

                await targetUser.send({ embeds: [dmEmbed] });
            } catch (error) {
                console.error('Failed to DM user:', error);
                // Continue execution even if DM fails
            }

            const embed = new EmbedBuilder()
                .setTitle('Ticket Removed')
                .setDescription(`
                    **User:** ${targetUser}
                    **Ticket Number:** ${ticketNumber}
                    **Original Offense:** ${removedTicket.offense}
                    **Amount:** $${removedTicket.price}
                    **Removal Reason:** ${reason}
                `)
                .setColor('#2B2D31')
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in ticketremove command:', error);
            try {
                const errorResponse = {
                    content: 'An error occurred while removing the ticket.',
                    ephemeral: true
                };
                
                if (interaction.deferred) {
                    await interaction.editReply(errorResponse);
                } else if (!interaction.replied) {
                    await interaction.reply(errorResponse);
                }
            } catch (e) {
                console.error('Failed to send error response:', e);
            }
        }
    }
};
