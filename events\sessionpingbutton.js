const { Events } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

const ROLE_ID = '1264023033006522369';
const JSON_PATH = path.join(__dirname, '../data/sessionPingRoles.json');

// MongoDB Schema
const userRoleSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    hasRole: { type: Boolean, default: false },
    updatedAt: { type: Date, default: Date.now }
});

const UserRole = mongoose.models.UserRole || mongoose.model('UserRole', userRoleSchema);

// Helper function to save role status
async function saveRoleStatus(userId, hasRole) {
    if (mongoose.connection.readyState === 1) {
        try {
            await UserRole.findOneAndUpdate(
                { userId },
                { hasRole, updatedAt: new Date() },
                { upsert: true }
            );
            return true;
        } catch (error) {
            console.error('MongoDB save error:', error);
            return false;
        }
    }
    
    // JSON fallback
    try {
        const dir = path.dirname(JSON_PATH);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        let data = {};
        if (fs.existsSync(JSON_PATH)) {
            data = JSON.parse(fs.readFileSync(JSON_PATH, 'utf8'));
        }
        
        data[userId] = { hasRole, updatedAt: new Date() };
        fs.writeFileSync(JSON_PATH, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('JSON fallback error:', error);
        return false;
    }
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        // Skip if it's not a button interaction or if it's from the release command
        if (!interaction.isButton()) return;
        if (interaction.message.interaction?.commandName === 'release') return;
        if (interaction.customId !== 'session_ping') return;

        try {
            // Validate guild context first
            if (!interaction.guild || !interaction.member) {
                return await interaction.reply({ 
                    content: 'This command can only be used in a server.',
                    ephemeral: true
                });
            }

            const role = interaction.guild.roles.cache.get(ROLE_ID);
            if (!role) {
                return await interaction.reply({ 
                    content: 'The session ping role could not be found. Please contact an administrator.',
                    ephemeral: true
                });
            }

            await interaction.deferReply({ ephemeral: true });

            const hasRole = interaction.member.roles.cache.has(ROLE_ID);
            await interaction.member.roles[hasRole ? 'remove' : 'add'](ROLE_ID);
            
            // Save the role status
            await saveRoleStatus(interaction.user.id, !hasRole);
            
            await interaction.editReply({ 
                content: hasRole 
                    ? `Successfully removed the ${role.name} role.`
                    : `Successfully added the ${role.name} role.`
            });

        } catch (error) {
            console.error('Error in session ping button handler:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ 
                    content: 'An error occurred while managing the role. Please try again later.',
                    ephemeral: true 
                });
            } else if (interaction.deferred) {
                await interaction.editReply({
                    content: 'An error occurred while managing the role. Please try again later.'
                });
            }
        }
    }
};
