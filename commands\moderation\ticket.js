const { <PERSON>lash<PERSON><PERSON>mand<PERSON><PERSON>er, Embed<PERSON>uilder, PermissionFlagsBits } = require('discord.js');
const path = require('path');
const fs = require('fs');
const Ticket = require('../../models/Ticket');
const mongoose = require('mongoose');

const ticketsDirPath = path.join(__dirname, '../../data/tickets');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Issue a ticket to a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select a user')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for the ticket')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount to pay')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const staffRoleId = '1237979199747129396';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.editReply({
                    content: 'You do not have permission to use this command.',
                    ephemeral: true
                });
            }

            const targetUser = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason');
            const amount = interaction.options.getInteger('amount');

            const newTicket = {
                userId: targetUser.id,
                offense: reason,
                price: amount,
                date: new Date(),
                issuedBy: interaction.user.id
            };

            // Try to save to MongoDB first
            if (mongoose.connection.readyState === 1) {
                await Ticket.create(newTicket);
            } else {
                // Fallback to JSON if MongoDB is not connected
                if (!fs.existsSync(ticketsDirPath)) {
                    fs.mkdirSync(ticketsDirPath, { recursive: true });
                }

                const ticketFilePath = path.join(ticketsDirPath, `${targetUser.id}.json`);
                let tickets = [];
                if (fs.existsSync(ticketFilePath)) {
                    tickets = JSON.parse(fs.readFileSync(ticketFilePath, 'utf8'));
                }

                tickets.push(newTicket);
                fs.writeFileSync(ticketFilePath, JSON.stringify(tickets, null, 2));
            }

            // DM the user
            try {
                const dmEmbed = new EmbedBuilder()
                    .setTitle('Ticket Issued')
                    .setDescription(`
                        **Reason:** ${reason}
                        **Amount to Pay:** $${amount}
                        **Issued By:** ${interaction.user.tag}
                        **Date:** ${new Date().toLocaleString()}
                    `)
                    .setColor('#FF0000')
                    .setTimestamp();

                await targetUser.send({ embeds: [dmEmbed] });
            } catch (error) {
                console.error('Failed to DM user:', error);
                // Continue execution even if DM fails
            }

            // Reply to command
            const embed = new EmbedBuilder()
                .setTitle('Ticket Issued')
                .setDescription(`
                    **User:** ${targetUser}
                    **Reason:** ${reason}
                    **Amount:** $${amount}
                    **Issued By:** ${interaction.user}
                `)
                .setColor('#2B2D31')
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in ticket command:', error);
            try {
                const errorResponse = {
                    content: 'An error occurred while issuing the ticket.',
                    ephemeral: true
                };
                
                if (interaction.deferred) {
                    await interaction.editReply(errorResponse);
                } else if (!interaction.replied) {
                    await interaction.reply(errorResponse);
                }
            } catch (e) {
                console.error('Failed to send error response:', e);
            }
        }
    }
};

