const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const Vehicle = require('../../models/Vehicle'); // Make sure this path is correct

const dataDirPath = path.join(__dirname, '../../data/vehicleData');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('register')
        .setDescription('Register a vehicle')
        .addIntegerOption(option =>
            option.setName('year')
                .setDescription('Vehicle year')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('make')
                .setDescription('Vehicle make')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('model')
                .setDescription('Vehicle model')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('color')
                .setDescription('Vehicle color')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('number-plate')
                .setDescription('Vehicle number plate')
                .setRequired(true)),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const year = interaction.options.getInteger('year');
            const make = interaction.options.getString('make');
            const model = interaction.options.getString('model');
            const color = interaction.options.getString('color');
            const numberPlate = interaction.options.getString('number-plate');
            const userId = interaction.user.id;

            const vehicleData = {
                year,
                make,
                model,
                color,
                numberPlate // Using consistent property name
            };

            // MongoDB storage
            if (global.storageMode === 'mongodb' && mongoose.connection.readyState === 1) {
                try {
                    let userVehicles = await Vehicle.findOne({ userId: userId });
                    
                    if (!userVehicles) {
                        userVehicles = new Vehicle({
                            userId: userId,
                            vehicles: [vehicleData]
                        });
                    } else {
                        userVehicles.vehicles.push(vehicleData);
                    }
                    
                    await userVehicles.save();
                } catch (error) {
                    console.error('MongoDB save error:', error);
                    // Fall back to JSON storage if MongoDB fails
                    global.storageMode = 'json';
                }
            }

            // JSON storage
            if (global.storageMode === 'json') {
                if (!fs.existsSync(dataDirPath)) {
                    fs.mkdirSync(dataDirPath, { recursive: true });
                }

                const userFilePath = path.join(dataDirPath, `${userId}.json`);
                let existingVehicles = [];

                if (fs.existsSync(userFilePath)) {
                    existingVehicles = JSON.parse(fs.readFileSync(userFilePath, 'utf8'));
                }

                existingVehicles.push(vehicleData);
                fs.writeFileSync(userFilePath, JSON.stringify(existingVehicles, null, 2), 'utf8');
            }

            const embed = new EmbedBuilder()
                .setColor('#2f3136')
                .setTitle('Vehicle Registration Successful')
                .setDescription(`Your ${year} ${make} ${model} has been registered successfully.`);

            await interaction.editReply({ embeds: [embed], ephemeral: true });

        } catch (error) {
            console.error('Registration error:', error);
            await interaction.editReply({
                content: 'An error occurred while registering your vehicle.',
                ephemeral: true
            });
        }
    },
};
