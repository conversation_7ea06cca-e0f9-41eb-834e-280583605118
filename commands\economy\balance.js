const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getBalance } = require('../../utils/economy');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('balance')
        .setDescription('Check your or another user\'s balance')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to check balance for')
                .setRequired(false)),

    async execute(interaction) {
        const target = interaction.options.getUser('user') || interaction.user;
        const balance = await getBalance(target.id);

        const embed = new EmbedBuilder()
            .setColor('#2B2D31')
            .setTitle(`${target.username}'s Balance`)
            .setDescription(`💰 Wallet: $${balance.wallet.toLocaleString()}\n🏦 Bank: $${balance.bank.toLocaleString()}`)
            .setFooter({ text: 'Southwest Florida Roleplay Realm' });

        await interaction.reply({ embeds: [embed] });
    },
};