{"name": "discord-api-types", "version": "0.37.83", "description": "Discord API typings that are kept up to date for use in bot library creation.", "homepage": "https://discord-api-types.dev", "exports": {"./globals": {"types": "./globals.d.ts", "require": "./globals.js", "import": "./globals.mjs"}, "./v6": {"types": "./v6.d.ts", "require": "./v6.js", "import": "./v6.mjs"}, "./v8": {"types": "./v8.d.ts", "require": "./v8.js", "import": "./v8.mjs"}, "./v9": {"types": "./v9.d.ts", "require": "./v9.js", "import": "./v9.mjs"}, "./v10": {"types": "./v10.d.ts", "require": "./v10.js", "import": "./v10.mjs"}, "./gateway": {"types": "./gateway/index.d.ts", "require": "./gateway/index.js", "import": "./gateway/index.mjs"}, "./gateway/v*": {"types": "./gateway/v*.d.ts", "require": "./gateway/v*.js", "import": "./gateway/v*.mjs"}, "./payloads": {"types": "./payloads/index.d.ts", "require": "./payloads/index.js", "import": "./payloads/index.mjs"}, "./payloads/v*": {"types": "./payloads/v*/index.d.ts", "require": "./payloads/v*/index.js", "import": "./payloads/v*/index.mjs"}, "./rest": {"types": "./rest/index.d.ts", "require": "./rest/index.js", "import": "./rest/index.mjs"}, "./rest/v*": {"types": "./rest/v*/index.d.ts", "require": "./rest/v*/index.js", "import": "./rest/v*/index.mjs"}, "./rpc": {"types": "./rpc/index.d.ts", "require": "./rpc/index.js", "import": "./rpc/index.mjs"}, "./rpc/v*": {"types": "./rpc/v*.d.ts", "require": "./rpc/v*.js", "import": "./rpc/v*.mjs"}, "./voice": {"types": "./voice/index.d.ts", "require": "./voice/index.js", "import": "./voice/index.mjs"}, "./voice/v*": {"types": "./voice/v*.d.ts", "require": "./voice/v*.js", "import": "./voice/v*.mjs"}, "./utils": {"types": "./utils/index.d.ts", "require": "./utils/index.js", "import": "./utils/index.mjs"}, "./utils/v*": {"types": "./utils/v*.d.ts", "require": "./utils/v*.js", "import": "./utils/v*.mjs"}}, "scripts": {"build:ci": "tsc --noEmit --incremental false", "build:deno": "node ./scripts/deno.mjs", "build:node": "tsc && run-p esm:*", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "ci:pr": "run-s changelog lint build:deno && node ./scripts/bump-website-version.mjs", "clean:deno": "<PERSON><PERSON>f deno/", "clean:node": "rimraf --glob \"{gateway,payloads,rest,rpc,voice,utils}/**/*.{js,mjs,d.ts,*map}\" \"{globals,v*}.{js,mjs,d.ts,*map}\"", "clean": "run-p clean:*", "esm:gateway": "gen-esm-wrapper ./gateway/index.js ./gateway/index.mjs", "esm:globals": "gen-esm-wrapper ./globals.js ./globals.mjs", "esm:payloads": "gen-esm-wrapper ./payloads/index.js ./payloads/index.mjs", "esm:rest": "gen-esm-wrapper ./rest/index.js ./rest/index.mjs", "esm:rpc": "gen-esm-wrapper ./rpc/index.js ./rpc/index.mjs", "esm:utils": "gen-esm-wrapper ./utils/index.js ./utils/index.mjs", "esm:versions": "node ./scripts/versions.mjs", "esm:voice": "gen-esm-wrapper ./voice/index.js ./voice/index.mjs", "lint": "prettier --write . && eslint --fix --ext mjs,ts \"{gateway,payloads,rest,rpc,voice,utils}/**/*.ts\" \"{globals,v*}.ts\" \"scripts/**/*.mjs\"", "postpublish": "run-s clean:node build:deno", "prepare": "tsc -p ./.eslint-plugin-local && (is-ci || husky)", "prepublishOnly": "run-s clean test:lint build:node", "test:lint": "prettier --check . && eslint --ext mjs,ts \"{gateway,payloads,rest,rpc,voice,utils}/**/*.ts\" \"{globals,v*}.ts\" \"scripts/**/*.mjs\"", "pretest:types": "tsc", "test:types": "tsd -t ./v10.d.ts", "posttest:types": "npm run clean:node"}, "keywords": ["discord", "discord api", "types", "discordjs"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "files": ["{gateway,payloads,rest,rpc,voice,utils}/**/*.{js,js.map,d.ts,d.ts.map,mjs}", "{globals,v*}.{js,js.map,d.ts,d.ts.map,mjs}"], "devDependencies": {"@commitlint/cli": "^19.0.3", "@commitlint/config-angular": "^19.0.3", "@favware/npm-deprecate": "^1.0.7", "@octokit/action": "^6.0.7", "@octokit/webhooks-types": "^7.3.2", "@sapphire/prettier-config": "^2.0.0", "@types/conventional-recommended-bump": "^9.0.3", "@types/node": "^20.11.24", "@typescript-eslint/utils": "^7.1.1", "conventional-changelog-cli": "^4.1.0", "conventional-recommended-bump": "^9.0.0", "eslint": "^8.57.0", "eslint-config-neon": "^0.1.59", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-local": "^4.2.1", "gen-esm-wrapper": "^1.1.3", "husky": "^9.0.11", "is-ci": "^3.0.1", "lint-staged": "^15.2.2", "npm-run-all2": "^6.0.0", "prettier": "^3.2.5", "pretty-quick": "^4.0.0", "rimraf": "^5.0.5", "tsd": "^0.31.0", "tsutils": "^3.21.0", "typescript": "^5.3.3"}, "publishConfig": {"provenance": true}, "repository": {"type": "git", "url": "https://github.com/discordjs/discord-api-types"}, "lint-staged": {"{gateway,payloads,rest,rpc,voice,utils}/**/*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts", "{globals,v*}.ts": "eslint --fix --ext mjs,js,ts"}, "commitlint": {"extends": ["@commitlint/config-angular"], "rules": {"type-enum": [2, "always", ["chore", "build", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test", "types", "wip"]], "scope-case": [1, "always", "pascal-case"]}}, "tsd": {"directory": "tests"}}