document.addEventListener('DOMContentLoaded', () => {
    loadVehicles();

    const form = document.getElementById('addVehicleForm');
    if (form) {
        form.addEventListener('submit', handleVehicleSubmit);
    }
});

async function loadVehicles() {
    try {
        const response = await fetch('/api/vehicles', {
            credentials: 'include'
        });
        const vehicles = await response.json();
        displayVehicles(vehicles);
    } catch (error) {
        console.error('Error loading vehicles:', error);
        showError('Failed to load vehicles');
    }
}

async function handleVehicleSubmit(e) {
    e.preventDefault();
    
    const formData = {
        year: parseInt(document.getElementById('year').value),
        make: document.getElementById('make').value,
        model: document.getElementById('model').value,
        color: document.getElementById('color').value,
        numberPlate: document.getElementById('numberPlate').value
    };

    try {
        const response = await fetch('/api/vehicles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error);
        }

        // Reset form and reload vehicles
        e.target.reset();
        loadVehicles();
        showSuccess('Vehicle registered successfully');
    } catch (error) {
        console.error('Error registering vehicle:', error);
        showError(error.message);
    }
}

async function deleteVehicle(id) {
    if (!confirm('Are you sure you want to delete this vehicle?')) {
        return;
    }

    try {
        const response = await fetch(`/api/vehicles/${id}`, {
            method: 'DELETE',
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to delete vehicle');
        }

        loadVehicles();
        showSuccess('Vehicle deleted successfully');
    } catch (error) {
        console.error('Error deleting vehicle:', error);
        showError('Failed to delete vehicle');
    }
}

function displayVehicles(vehicles) {
    const vehicleList = document.getElementById('vehicles');
    if (!vehicleList) return;

    vehicleList.innerHTML = vehicles.length ? '' : '<p>No vehicles registered</p>';

    vehicles.forEach(vehicle => {
        const vehicleElement = document.createElement('div');
        vehicleElement.className = 'vehicle-card';
        vehicleElement.innerHTML = `
            <h3>${vehicle.year} ${vehicle.make} ${vehicle.model}</h3>
            <p>Color: ${vehicle.color}</p>
            <p>Plate: ${vehicle.numberPlate}</p>
            <button onclick="deleteVehicle('${vehicle._id}')" class="delete-btn">Delete</button>
        `;
        vehicleList.appendChild(vehicleElement);
    });
}

function showError(message) {
    // Implement your error notification system here
    alert(message);
}

function showSuccess(message) {
    // Implement your success notification system here
    alert(message);
}