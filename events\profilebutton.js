const { Events, Embed<PERSON>uilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');
const Ticket = require('../models/Ticket');
const Vehicle = require('../models/Vehicle');
const unbelievaboatAPI = require('../utils/unbelievaboat');

// Cache for user data
const userDataCache = {
    tickets: new Map(),
    vehicles: new Map()
};

const loadUserData = async (userId, dataType) => {
    try {
        // Try MongoDB first
        if (global.storageMode === 'mongodb' && mongoose.connection.readyState === 1) {
            const Model = dataType === 'vehicles' ? Vehicle : Ticket;
            const data = await Model.findOne({ userId: userId });
            
            if (dataType === 'vehicles') {
                return data ? data.vehicles : [];
            } else {
                return data ? data.tickets : [];
            }
        }
        
        // Fallback to JSON if MongoDB is not connected
        const dataPath = path.join(__dirname, '..', 'data', 
            dataType === 'vehicles' ? 'vehicleData' : 'tickets', 
            `${userId}.json`
        );

        if (fs.existsSync(dataPath)) {
            const rawData = fs.readFileSync(dataPath, 'utf8');
            const parsed = JSON.parse(rawData);
            return Array.isArray(parsed) ? parsed : [];
        }
        return [];
    } catch (error) {
        console.error(`Error loading ${dataType} data:`, error);
        return [];
    }
};

const createEmbed = (title, description, user) => {
    const embed = new EmbedBuilder()
        .setColor('#35506e');
    
    if (title) embed.setTitle(title);
    if (description && description.length > 0) {
        embed.setDescription(description);
    } else {
        embed.setDescription('No information available.');
    }
    
    if (user) {
        embed.setFooter({ 
            text: `Discord ID: ${user.id}`,
            iconURL: user.displayAvatarURL()
        });
    }
    
    return embed;
};

const ITEMS_PER_PAGE = 4; // Consistent page size for both vehicles and tickets

const createPaginationButtons = (userId, currentPage, totalPages, type) => {
    const row = new ActionRowBuilder();
    
    if (totalPages <= 1) return row;
    
    if (currentPage > 0) {
        row.addComponents(
            new ButtonBuilder()
                .setCustomId(`prev_${type}_${userId}_${currentPage}`)
                .setLabel('◀️ Previous')
                .setStyle(ButtonStyle.Secondary)
        );
    }
    
    if (currentPage < totalPages - 1) {
        row.addComponents(
            new ButtonBuilder()
                .setCustomId(`next_${type}_${userId}_${currentPage}`)
                .setLabel('Next ▶️')
                .setStyle(ButtonStyle.Secondary)
        );
    }
    
    return row;
};

module.exports = {
    name: Events.InteractionCreate,
    userDataCache,
    async execute(interaction) {
        if (!interaction.isButton()) return;

        if (!interaction.customId.startsWith('show_') && 
            !interaction.customId.includes('_registration_') && 
            !interaction.customId.includes('_ticket_') &&
            !interaction.customId.startsWith('show_balance_') &&
            !interaction.customId.startsWith('auth_discord_')) {
            return;
        }

        try {
            // Handle Discord auth button
            if (interaction.customId === 'auth_discord') {
                await interaction.reply({
                    content: 'Please authenticate with Discord: http://localhost:3000/auth/discord',
                    ephemeral: true
                });
                return;
            }

            const parts = interaction.customId.split('_');
            const userId = parts[2];
            const member = await interaction.guild.members.fetch(userId);

            // Create auth button if needed
            const authButton = new ButtonBuilder()
                .setCustomId('auth_discord')
                .setLabel('Authenticate with Discord')
                .setStyle(ButtonStyle.Primary);

            // Add auth button to existing buttons
            const createButtons = (userId) => {
                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`show_registrations_${userId}`)
                            .setLabel('View Registrations')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_tickets_${userId}`)
                            .setLabel('View Tickets')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_balance_${userId}`)
                            .setLabel('View Balance')
                            .setStyle(ButtonStyle.Secondary),
                        authButton
                    );
                return row;
            };

            // Handle balance button
            if (interaction.customId.startsWith('show_balance_')) {
                await interaction.deferReply({ ephemeral: true });
                
                const guildId = interaction.guild.id;
                const balance = await unbelievaboatAPI.getBalance(guildId, userId);
                
                if (balance.error) {
                    await interaction.editReply({
                        content: 'Unable to fetch balance information at this time. Please try again later.',
                        ephemeral: true
                    });
                    return;
                }

                const balanceEmbed = new EmbedBuilder()
                    .setColor('#35506e')
                    .setTitle('💰 Balance Information')
                    .addFields(
                        { 
                            name: '💵 Wallet', 
                            value: `$${balance.wallet.toLocaleString()}`, 
                            inline: true 
                        },
                        { 
                            name: '🏦 Bank', 
                            value: `$${balance.bank.toLocaleString()}`, 
                            inline: true 
                        },
                        {
                            name: '💰 Total',
                            value: `$${balance.total.toLocaleString()}`,
                            inline: true
                        }
                    )
                    .setTimestamp();

                await interaction.editReply({
                    embeds: [balanceEmbed],
                    components: [createButtons(userId)],
                    ephemeral: true
                });
                return;
            }

            console.log(`Processing button interaction for user ${userId}`);

            // Clear cache before loading data to ensure fresh data
            if (userDataCache.tickets.has(userId)) userDataCache.tickets.delete(userId);
            if (userDataCache.vehicles.has(userId)) userDataCache.vehicles.delete(userId);

            // Handle initial button clicks
            if (interaction.customId.startsWith('show_')) {
                if (interaction.customId.startsWith('show_registrations_')) {
                    const vehicleEmbed = await createVehicleEmbed(userId);
                    await interaction.reply({
                        embeds: [vehicleEmbed],
                        components: [createButtons(userId)],
                        ephemeral: true
                    });
                } 
                else if (interaction.customId.startsWith('show_tickets_')) {
                    console.log('Loading tickets for display');
                    const tickets = await loadUserData(userId, 'tickets');
                    console.log(`Found ${tickets.length} tickets`);
                    
                    const totalPages = Math.ceil(tickets.length / ITEMS_PER_PAGE);
                    const components = [];
                    const paginationRow = createPaginationButtons(userId, 0, totalPages, 'ticket');
                    if (paginationRow.components.length > 0) {
                        components.push(paginationRow);
                    }

                    await interaction.reply({
                        embeds: await createTicketEmbed(tickets, 0),
                        components: components,
                        ephemeral: true
                    });
                }
            }
            // Handle pagination
            else if (interaction.customId.includes('_registration_') || interaction.customId.includes('_ticket_')) {
                await interaction.deferUpdate();
                const currentPage = parseInt(parts[3]);
                const isNext = interaction.customId.startsWith('next');
                const newPage = isNext ? currentPage + 1 : currentPage - 1;

                if (interaction.customId.includes('_registration_')) {
                    const vehicleData = await loadUserData(userId, 'vehicles');
                    const totalPages = Math.ceil(vehicleData.length / ITEMS_PER_PAGE);
                    
                    await interaction.editReply({
                        embeds: await createVehicleEmbed(vehicleData, newPage),
                        components: [createPaginationButtons(
                            userId,
                            newPage,
                            totalPages,
                            'registration'
                        )]
                    });
                } else if (interaction.customId.includes('_ticket_')) {
                    const tickets = await loadUserData(userId, 'tickets');
                    const totalPages = Math.ceil(tickets.length / ITEMS_PER_PAGE);
                    
                    await interaction.editReply({
                        embeds: await createTicketEmbed(tickets, newPage),
                        components: [createPaginationButtons(userId, newPage, totalPages, 'ticket')]
                    });
                }
            }
        } catch (error) {
            console.error('Error handling button interaction:', error);
            await handleError(interaction, error);
        }
    }
};

async function createVehicleEmbed(userId) {
    try {
        const vehicles = await Vehicle.find({ userId });
        
        if (!vehicles || vehicles.length === 0) {
            return new EmbedBuilder()
                .setColor('#35506e')
                .setTitle('Vehicle Registration')
                .setDescription('No vehicles registered. Use `/register` to register a vehicle or visit the dashboard.');
        }

        const embed = new EmbedBuilder()
            .setColor('#35506e')
            .setTitle('Registered Vehicles')
            .setDescription('Here are your registered vehicles:');

        vehicles.forEach((vehicle, index) => {
            embed.addFields({
                name: `Vehicle ${index + 1}`,
                value: `Year: ${vehicle.year}\nMake: ${vehicle.make}\nModel: ${vehicle.model}\nColor: ${vehicle.color}\nPlate: ${vehicle.numberPlate}`,
                inline: true
            });
        });

        return embed;
    } catch (error) {
        console.error('Error creating vehicle embed:', error);
        return new EmbedBuilder()
            .setColor('#ed4245')
            .setTitle('Error')
            .setDescription('Failed to fetch vehicle information');
    }
}

async function createTicketEmbed(tickets = [], page = 0) {
    const itemsPerPage = ITEMS_PER_PAGE;
    const totalPages = Math.ceil(tickets.length / itemsPerPage);
    
    if (!tickets || tickets.length === 0) {
        return [createEmbed(null, 'No tickets have been given to this user.')];
    }
    
    const pageTickets = tickets.slice(page * itemsPerPage, (page * itemsPerPage) + itemsPerPage);
    
    const embeds = pageTickets.map((ticket, index) => {
        // Add null checks and default values
        const ticketDetails = {
            offense: ticket.offense || ticket.reason || 'No offense specified',
            price: ticket.price || ticket.fine || 0,
            date: ticket.date ? new Date(ticket.date).toLocaleString() : 'No date specified',
            issuedBy: ticket.issuedBy || ticket.officer || 'Unknown'
        };

        const numericPrice = Number(ticketDetails.price);
        const formattedPrice = `$${numericPrice.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        })}`;

        const embed = new EmbedBuilder()
            .setColor('#35506e')
            .setTitle(`Ticket #${(page * itemsPerPage) + index + 1}`);

        // Only add fields if they have valid values
        if (ticketDetails.offense) {
            embed.addFields({ name: 'Offense', value: ticketDetails.offense });
        }
        if (ticketDetails.price) {
            embed.addFields({ name: 'Fine', value: formattedPrice });
        }
        if (ticketDetails.date) {
            embed.addFields({ name: 'Issued', value: ticketDetails.date });
        }
        if (ticketDetails.issuedBy) {
            embed.addFields({ 
                name: 'Issued By', 
                value: ticketDetails.issuedBy.startsWith('<@') ? 
                    ticketDetails.issuedBy : 
                    `<@${ticketDetails.issuedBy}>` 
            });
        }

        embed.setFooter({ text: `Page ${page + 1}/${totalPages}` });
        return embed;
    });

    return embeds;
}

async function handleInteractionReply(interaction, replyOptions) {
    try {
        if (!interaction.replied) {
            await interaction.reply(replyOptions);
        }
    } catch (error) {
        // Silently handle unknown interaction errors
        if (error.code !== 10062) {
            console.error('Error in handleInteractionReply:', error);
        }
    }
}

async function handleInteractionUpdate(interaction, updateOptions) {
    try {
        if (!interaction.replied) {
            await interaction.update(updateOptions);
        }
    } catch (error) {
        // Silently handle unknown interaction errors
        if (error.code !== 10062) {
            console.error('Error in handleInteractionUpdate:', error);
        }
    }
}

async function handleInteractionError(interaction) {
    try {
        if (!interaction.deferred) {
            await interaction.deferReply({ ephemeral: true });
        }
        await interaction.editReply({ 
            content: 'An error occurred while processing your request.',
            ephemeral: true 
        });
    } catch (error) {
        console.error('Error in handleInteractionError:', error);
    }
}

const handleError = async (interaction, error) => {
    console.error('Error:', error);
    
    const errorMessage = error.code === 'MONGODB_CONNECTION_ERROR' 
        ? 'Database connection error. Please try again later.'
        : 'An error occurred while processing your request.';
        
    if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
            content: errorMessage,
            ephemeral: true
        });
    } else {
        await interaction.followUp({
            content: errorMessage,
            ephemeral: true
        });
    }
};
