const { Events, EmbedBuilder } = require('discord.js');

// Pre-define constant embeds outside the execute function
const embedColor = '#2B2D31';
const footerText = 'Southwest Florida Roleplay Realm';

// Pre-define all possible responses
const responses = {
    'boost-perks': {
        embeds: [
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('**__1-3 Perks__**')
                .setDescription('If you boost this server 1-3 times, you will get the Server Boost role, Image Permission, Banned Vehicle Exempt, Early Access role, and 100k eco.')
                .setFooter({ text: footerText }),
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('**__4+ Boost Perks__**')
                .setDescription('If you boost 4+ times, you will get 1-3 benefits and the Ultra Banned Vehicle List role. You will also get 250k eco, and you will be able to register 7 cars.')
                .setFooter({ text: footerText })
        ]
    },
    'robux-perks': {
        embeds: [
            new EmbedBuilder()
                .setColor(embedColor)
                .setTitle('Robux Perks')
                .setDescription('Thank you for your Robux donations! Donors receive perks like access to private sessions, custom titles, and more.')
                .setFooter({ text: footerText })
        ]
    }
};

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'serverperk') return;

        try {
            // Immediate response without deferring
            await interaction.reply({
                ...responses[interaction.values[0]],
                ephemeral: true
            });
        } catch (error) {
            console.error('Error in server perks menu handler:', error);
            await interaction.reply({
                content: 'An error occurred while processing your request.',
                ephemeral: true
            }).catch(console.error);
        }
    }
};
