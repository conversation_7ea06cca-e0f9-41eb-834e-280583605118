const { Events, EmbedBuilder } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Define MongoDB Schema
const feedbackSchema = new mongoose.Schema({
    userId: String,
    host: String,
    rating: Number,
    improvement: String,
    notes: String,
    timestamp: { type: Date, default: Date.now }
});

const Feedback = mongoose.models.Feedback || mongoose.model('Feedback', feedbackSchema);

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isModalSubmit() || interaction.customId !== 'session_feedback_modal') return;

        try {
            const host = interaction.fields.getTextInputValue('host');
            const rating = interaction.fields.getTextInputValue('rating');
            const improvement = interaction.fields.getTextInputValue('improvement');
            const notes = interaction.fields.getTextInputValue('notes') || 'No additional notes provided';

            // Validate rating
            const numRating = parseInt(rating);
            if (isNaN(numRating) || numRating < 1 || numRating > 10) {
                await interaction.reply({
                    content: 'Please provide a valid rating between 1 and 10.',
                    ephemeral: true
                });
                return;
            }

            // Create feedback data
            const feedbackData = {
                userId: interaction.user.id,
                host,
                rating: numRating,
                improvement,
                notes,
                timestamp: new Date()
            };

            // Try to save to MongoDB first
            if (mongoose.connection.readyState === 1) {
                try {
                    await Feedback.create(feedbackData);
                    console.log('✅ Feedback saved to MongoDB');
                } catch (mongoError) {
                    console.error('❌ MongoDB save error:', mongoError);
                    // Fall through to JSON fallback
                    const feedbackDir = path.join(__dirname, '..', 'data', 'feedback');
                    if (!fs.existsSync(feedbackDir)) {
                        fs.mkdirSync(feedbackDir, { recursive: true });
                    }
                    const feedbackPath = path.join(feedbackDir, `${interaction.user.id}_${Date.now()}.json`);
                    fs.writeFileSync(feedbackPath, JSON.stringify(feedbackData, null, 2));
                    console.log('⚠️ Feedback saved to JSON file as fallback');
                }
            } else {
                // JSON fallback if MongoDB is not connected
                const feedbackDir = path.join(__dirname, '..', 'data', 'feedback');
                if (!fs.existsSync(feedbackDir)) {
                    fs.mkdirSync(feedbackDir, { recursive: true });
                }
                const feedbackPath = path.join(feedbackDir, `${interaction.user.id}_${Date.now()}.json`);
                fs.writeFileSync(feedbackPath, JSON.stringify(feedbackData, null, 2));
                console.log('⚠️ Feedback saved to JSON file (MongoDB not connected)');
            }

            // Create feedback embed
            const feedbackEmbed = new EmbedBuilder()
                .setTitle('Session Feedback Received')
                .setDescription(`Feedback from ${interaction.user}`)
                .addFields(
                    { name: 'Host', value: host, inline: true },
                    { name: 'Rating', value: `${rating}/10`, inline: true },
                    { name: 'Improvements Suggested', value: improvement },
                    { name: 'Additional Notes', value: notes }
                )
                .setColor('#3D426B')
                .setTimestamp();

            // Try to send feedback to channel
            const feedbackChannelId = '1253579753621950546';
            try {
                const feedbackChannel = await interaction.client.channels.fetch(feedbackChannelId);
                if (feedbackChannel) {
                    await feedbackChannel.send({ embeds: [feedbackEmbed] });
                }
            } catch (channelError) {
                console.error('Unable to send feedback to channel:', channelError);
                // Save the feedback embed to a file as fallback
                const feedbackDir = path.join(__dirname, '..', 'data', 'feedback_embeds');
                if (!fs.existsSync(feedbackDir)) {
                    fs.mkdirSync(feedbackDir, { recursive: true });
                }
                const embedPath = path.join(feedbackDir, `${interaction.user.id}_${Date.now()}_embed.json`);
                fs.writeFileSync(embedPath, JSON.stringify(feedbackEmbed.toJSON(), null, 2));
            }

            // Always reply to the user
            await interaction.reply({
                content: 'Thank you for your feedback! Your response has been recorded.',
                ephemeral: true
            });

        } catch (error) {
            console.error('Error handling feedback modal submission:', error);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: 'There was an error submitting your feedback, but we\'ve saved your response. Thank you!',
                        ephemeral: true
                    });
                } catch (err) {
                    console.error('Error sending error response:', err);
                }
            }
        }
    },
};

