/**
 * Test private server functionality and button customId fix
 */

// Test the customId length fix
function testCustomIdFix() {
  console.log("=== Testing CustomId Length Fix ===");
  
  const longLink = "https://www.roblox.com/games/126884695634066/Grow-a-Garden?privateServerLinkCode=81938929815212429967836931462396";
  console.log(`Original link length: ${longLink.length}`);
  
  // Simulate the fix
  const customId = `session_link_${Buffer.from(longLink).toString('base64').substring(0, 80)}`;
  console.log(`CustomId: ${customId}`);
  console.log(`CustomId length: ${customId.length}`);
  console.log(`✅ CustomId is ${customId.length <= 100 ? 'VALID' : 'INVALID'} (max 100 chars)`);
}

// Test private server detection
function testPrivateServerDetection() {
  console.log("\n=== Testing Private Server Detection ===");
  
  function extractServerInfoFromLink(link) {
    try {
      const url = new URL(link);
      const params = new URLSearchParams(url.search);
      
      const privateServerLinkCode = params.get('privateServerLinkCode');
      const accessCode = params.get('accessCode');
      const jobId = params.get('gameInstanceId');
      
      return {
        isPrivateServer: !!(privateServerLinkCode || accessCode),
        linkCode: privateServerLinkCode || accessCode,
        jobId: jobId,
        placeId: url.pathname.split('/')[2]
      };
    } catch (error) {
      console.error("Error parsing server link:", error);
      return { isPrivateServer: false, linkCode: null, jobId: null, placeId: null };
    }
  }
  
  const testLinks = [
    "https://www.roblox.com/games/126884695634066/Grow-a-Garden?privateServerLinkCode=81938929815212429967836931462396",
    "https://www.roblox.com/games/126884695634066/Game?accessCode=12345",
    "https://www.roblox.com/games/126884695634066/Game", // Public link
  ];
  
  testLinks.forEach((link, index) => {
    console.log(`\nTest ${index + 1}: ${link.substring(0, 80)}...`);
    const info = extractServerInfoFromLink(link);
    console.log(`  Is Private Server: ${info.isPrivateServer}`);
    console.log(`  Link Code: ${info.linkCode}`);
    console.log(`  Job ID: ${info.jobId}`);
    console.log(`  Place ID: ${info.placeId}`);
    
    if (info.isPrivateServer) {
      console.log(`  ✅ PRIVATE SERVER DETECTED`);
    } else {
      console.log(`  ❌ Public server (would use default stats)`);
    }
  });
}

// Test the new logic flow
function testNewLogic() {
  console.log("\n=== Testing New Logic Flow ===");
  
  function simulateGetRobloxServerStats(serverLink) {
    console.log(`\nProcessing: ${serverLink ? serverLink.substring(0, 50) + '...' : 'No link'}`);
    
    // If no server link provided, return default stats
    if (!serverLink) {
      console.log("❌ No server link provided, using default stats");
      return { fps: "60", playing: "0/25", ping: "50ms", isPrivateServer: false };
    }
    
    // Extract server info from the link
    function extractServerInfoFromLink(link) {
      try {
        const url = new URL(link);
        const params = new URLSearchParams(url.search);
        
        const privateServerLinkCode = params.get('privateServerLinkCode');
        const accessCode = params.get('accessCode');
        const jobId = params.get('gameInstanceId');
        
        return {
          isPrivateServer: !!(privateServerLinkCode || accessCode),
          linkCode: privateServerLinkCode || accessCode,
          jobId: jobId,
          placeId: url.pathname.split('/')[2]
        };
      } catch (error) {
        return { isPrivateServer: false, linkCode: null, jobId: null, placeId: null };
      }
    }
    
    const serverInfo = extractServerInfoFromLink(serverLink);
    
    // Only process private servers
    if (!serverInfo || !serverInfo.isPrivateServer) {
      console.log("❌ Not a private server link, using default stats");
      return { fps: "60", playing: "0/25", ping: "50ms", isPrivateServer: false };
    }
    
    console.log("✅ Private server detected, would fetch private server stats");
    return { 
      fps: "60", 
      playing: "Private Server", 
      ping: "~25ms", 
      serverCount: "Private",
      isPrivateServer: true 
    };
  }
  
  const testCases = [
    null, // No link
    "https://www.roblox.com/games/126884695634066/Game", // Public
    "https://www.roblox.com/games/126884695634066/Game?privateServerLinkCode=12345", // Private
  ];
  
  testCases.forEach((link, index) => {
    console.log(`\n--- Test Case ${index + 1} ---`);
    const result = simulateGetRobloxServerStats(link);
    console.log("Result:", JSON.stringify(result, null, 2));
  });
}

function runAllTests() {
  testCustomIdFix();
  testPrivateServerDetection();
  testNewLogic();
  console.log("\n=== All Tests Complete ===");
}

runAllTests();
