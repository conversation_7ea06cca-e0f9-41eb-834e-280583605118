const fetch = require('node-fetch');
require('dotenv').config();

class UnbelievaboatAPI {
    constructor() {
        this.apiKey = process.env.UNBELIEVABOAT_API_KEY;
        this.baseURL = 'https://unbelievaboat.com/api/v1';
    }

    async getBalance(guildId, userId) {
        try {
            const response = await fetch(`${this.baseURL}/guilds/${guildId}/users/${userId}`, {
                headers: {
                    'Authorization': `${this.apiKey}`,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return {
                wallet: data.cash || 0,
                bank: data.bank || 0
            };
        } catch (error) {
            console.error('Error fetching Unbelievaboat balance:', error);
            return { wallet: 0, bank: 0 };
        }
    }
}

const unbelievaboatAPI = new UnbelievaboatAPI();
module.exports = unbelievaboatAPI;
