const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonB<PERSON>er, ButtonStyle, MessageFlags } = require('discord.js');
const { MongoClient } = require('mongodb');
const path = require('path');
require('dotenv').config();

// MongoDB connection URI from environment variables
const uri = process.env.MONGODB_URI;
const dbName = process.env.DB_NAME || 'sfrr';
const collectionName = 'earlyAccessLinks';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('earlyaccess')
    .setDescription('Grant early access to a user with a link')
    .addStringOption(option =>
      option.setName('link')
        .setDescription('The link for early access')
        .setRequired(true)),
  async execute(interaction) {
    try {
      const staffRoleId = '1279933324298817608';

      if (!interaction.member.roles.cache.has(staffRoleId)) {
        return await interaction.reply({ 
          content: 'You do not have permission to execute this command.', 
          flags: MessageFlags.Ephemeral 
        });
      }

      await interaction.reply({ content: 'Early access released!', ephemeral: true });

      const link = interaction.options.getString('link');
      const earlyAccessRoleId1 = '1281003485994942558';
      const earlyAccessRoleId2 = '1281002681879756955';

      const embed = new EmbedBuilder()
        .setTitle('Southwest Florida Roleplay Realm | Early Access Released!')
        .setDescription(`${interaction.user} has now released early access. To join, click on the button below called "Early Access Link". Once you have loaded in, park up and wait until the host has released the session to everyone. Make sure not to leak the link that the host provides to people that aren't on the server and that don't have access to early access.`)
        .setColor('#2B2D31')
        .setImage('https://cdn.discordapp.com/attachments/1277470399952850998/1277483395194290258/image.png?ex=66cd549f&is=66cc031f&hm=bfc0a1e25a81390e702772500a786f71671f89e5faae727a4fe41555da982f0e&')
        .setFooter({
          text: 'Southwest Florida Roleplay Realm',
          iconURL: 'https://cdn.discordapp.com/attachments/893617400321290311/1287887899551076445/Screenshot_2024-09-14_150918.png?ex=66f32e93&is=66f1dd13&hm=b4b9c6b0052461ab8c67620e28ff2c7ffdece8ed2ca7055760fe046b7a20e925&'
        })
        .setTimestamp();

      const button = new ButtonBuilder()
        .setLabel('Early Access Link')
        .setStyle(ButtonStyle.Primary)
        .setCustomId('early_access_link');

      const row = new ActionRowBuilder().addComponents(button);

      const message = await interaction.channel.send({
        content: `<@&${earlyAccessRoleId1}> <@&${earlyAccessRoleId2}>`,
        embeds: [embed],
        components: [row]
      });

      // Store the link in MongoDB
      try {
        const client = new MongoClient(uri);
        await client.connect();
        
        const db = client.db(dbName);
        const collection = db.collection(collectionName);
        
        // Store the link with the message ID as the identifier
        await collection.updateOne(
          { messageId: message.id },
          { $set: { 
              messageId: message.id,
              link: link,
              createdAt: new Date(),
              createdBy: interaction.user.id
            }
          },
          { upsert: true }
        );
        
        await client.close();
      } catch (error) {
        console.error('Error storing link in MongoDB:', error);
      }

      // Log the command execution
      const logChannelId = '1279642823951646760';
      const logChannel = interaction.guild.channels.cache.get(logChannelId);
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setTitle('Command Executed')
          .setDescription('The `/earlyaccess` command was executed.')
          .addFields(
            { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
            { name: 'User ID', value: `${interaction.user.id}`, inline: true },
            { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
            { name: 'Link Provided', value: `${link}`, inline: false },
          )
          .setColor('#2B2D31')
          .setTimestamp();

        logChannel.send({ embeds: [logEmbed] });
      }

    } catch (error) {
      console.error('Error executing command:', error);
      if (!interaction.replied) {
        await interaction.reply({ content: 'There was an error while executing this command!', ephemeral: true });
      }
    }
  }
};
