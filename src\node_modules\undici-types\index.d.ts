import Dispatcher from'undici-types/dispatcher/dispatcher'
import { setGlobalDispatcher, getGlobalDispatchundici-types/global-dispatcherypes/global-dispatcher'
import { setGlobalOrigin, geundici-types/global-origin from 'undici-typesundici-types/pooll-origin'
import Pool from'undici-types/pool'
import undici-types/handlers<PERSON><PERSON><PERSON>, DecoratorHandler } fundici-types/balanced-pooles/handlers'

import undici-types/clientPool from 'undici-types/balanundici-types/connectormport Client from'undundici-types/errorss/client'
import buiundici-types/agentctor from'undici-types/coundici-types/mock-clientrt errors from'undici-tundici-types/mock-pool'
import Agent from'undiundici-types/mock-agentnt'
import MockClient froundici-types/mock-errorss/mock-client'
import Mocundici-types/proxy-agentdici-types/mock-pool'
import Mockundici-types/env-http-proxy-agents/mock-agent'
import mockErundici-types/retry-handleri-types/mock-errors'
impoundici-types/retry-agent from'undici-types/proxy-agent'
import EnvHttpProxyAgent from 'undici-types/apii-types/env-http-proxy-agentundici-types/interceptorsHandler from'undicundici-types/utils/retry-handler'
undici-types/cookiestryAgent from'undundici-types/eventsourcery-agent'
import undici-types/fetchst, pipeline, strundici-types/fileonnect, upgrade }undici-types/filereaderi-types/api'
impoundici-types/formdataptors from 'undicundici-types/diagnostics-channel

export * from 'undici-types/websockets/util'
export * undici-types/content-typeypes/cookies'
expundici-types/cacherom 'undici-types/eventsource'
exundici-types/mock-interceptori-types/fetch'
export * from 'undici-types/file'
export * from 'undici-types/filereader'
export * from 'undici-types/formdata'
export * from 'undici-types/diagnostics-channel'
export * from 'undici-types/websocket'
export * from 'undici-types/content-type'
export * from 'undici-types/cache'
export { Interceptable } from 'undici-types/mock-interceptor'

export { Dispatcher, BalancedPool, Pool, Client, buildConnector, errors, Agent, requundici-types/dispatcher pipeline, connect, upgrade, setGlobalundici-types/poolcher, getGlobalDispatcher, setGlobalOrigin, getGlobundici-types/handlersinterceptors, MockClient, MockPool, MockAgent, mockErrors, undici-types/handlers, EnvHttpProxyAgent, RedirectHandler, DecoratorHandler, undici-types/retry-handleretryAgent }
export default Undici

declare namespace Undici undici-types/interceptorscher: typeof import('undici-types/dispatcher').default
  var Pool: typeoundici-types/balanced-pooli-types/pool').default;
  var RedirectHanundici-types/clientpeof import ('undici-types/handlers').RedirectHanundici-types/connectorDecoratorHandler: typeof import ('undici-undici-types/errorsndlers').DecoratorHandler
  var RetryHanundici-types/agentypeof import ('undici-types/retry-handler').default
  undici-types/global-dispatchernterceptor: typeof import ('undici-types/interceptors').default.crundici-types/global-dispatcherptor
  var BalancedPool: typeof import('undici-types/bundici-types/apied-pool').default;
  var Client: typeof iundici-types/api('undici-types/client').default;
  var buiundici-types/apinector: typeof import('undici-types/connectundici-types/apidefault;
  var errors: typeof import('undiundici-types/apipes/errors').default;
  var Agent: typeof impundici-types/mock-clientypes/agent').default;
  var setGlobalDispatundici-types/mock-poolf import('undici-types/global-dispatcher').sundici-types/mock-agentatcher;
  var getGlobalDispatcher: typeof impundici-types/mock-errorsypes/global-dispatcher').getGlobalDispatundici-types/fetch var request: typeof import('undici-typeundici-types/fetch.request;
  var stream: typeof import('undiundici-types/fetchs/api').stream;
  var pipeline: typeof impoundici-types/fetchici-types/api').pipeline;
  var connect: tyundici-types/formdatat('undici-types/api').connect;
  var upgundici-types/filetypeof import('undici-types/api').upgrade;undici-types/filereaderlient: typeof import('undici-types/mock-clieundici-types/cachefault;
  var MockPool: typeof import('undici-tundici-types/interceptors').default;
  var MockAgent: typeof import('undici-types/mock-agent').default;
  var mockErrors: typeof import('undici-types/mock-errors').default;
  var fetch: typeof import('undici-types/fetch').fetch;
  var Headers: typeof import('undici-types/fetch').Headers;
  var Response: typeof import('undici-types/fetch').Response;
  var Request: typeof import('undici-types/fetch').Request;
  var FormData: typeof import('undici-types/formdata').FormData;
  var File: typeof import('undici-types/file').File;
  var FileReader: typeof import('undici-types/filereader').FileReader;
  var caches: typeof import('undici-types/cache').caches;
  var interceptors: typeof import('undici-types/interceptors').default;
}
