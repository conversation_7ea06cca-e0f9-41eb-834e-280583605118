{"version": 3, "file": "client_metadata.js", "sourceRoot": "", "sources": ["../../../src/cmap/handshake/client_metadata.ts"], "names": [], "mappings": ";;;AA6FA,gDA+DC;AAuBD,oDAwBC;AAMD,gCA0EC;AA3RD,yBAAyB;AACzB,mCAAmC;AAEnC,qCAAwD;AACxD,uCAAwD;AAExD,uCAA+C;AAE/C,iEAAiE;AACjE,MAAM,mBAAmB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC;AAyCrE,gBAAgB;AAChB,MAAa,mBAAmB;IAI9B,YAAoB,OAAe;QAAf,YAAO,GAAP,OAAO,CAAQ;QAH3B,aAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,uCAAuC;QAC/B,iBAAY,GAAG,CAAC,CAAC;IACa,CAAC;IAEvC,sEAAsE;IAC/D,cAAc,CAAC,GAAW,EAAE,KAAmC;QACpE,2FAA2F;QAC3F,+DAA+D;QAC/D,MAAM,cAAc,GAAG,WAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;QAEhF,IAAI,cAAc,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,YAAY,IAAI,cAAc,CAAC;QAEpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ;QACN,OAAO,WAAI,CAAC,WAAW,CAAC,WAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrD,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,KAAK;YACrB,aAAa,EAAE,KAAK;YACpB,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AA/BD,kDA+BC;AAGD;;;;;;;GAOG;AACH,SAAgB,kBAAkB,CAAC,OAAkC;IACnE,MAAM,gBAAgB,GAAG,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAEtD,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IACjC,sCAAsC;IACtC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,GACR,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,GAAG;YACvC,CAAC,CAAC,OAAO,CAAC,OAAO;YACjB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrE,gBAAgB,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;IAEtE,MAAM,UAAU,GAAG;QACjB,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ;QACnD,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,mBAAmB,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,mBAAmB;KACxF,CAAC;IAEF,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;QAC3D,MAAM,IAAI,iCAAyB,CACjC,iFAAiF,CAClF,CAAC;IACJ,CAAC;IAED,IAAI,WAAW,GAAG,cAAc,EAAE,CAAC;IACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,WAAW,GAAG,GAAG,WAAW,IAAI,QAAQ,EAAE,CAAC;IAC7C,CAAC;IAED,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,CAAC;QAC9D,MAAM,IAAI,iCAAyB,CACjC,yEAAyE,CAC1E,CAAC;IACJ,CAAC;IAED,sFAAsF;IACtF,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE;SACrB,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC;SAC7B,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC;SACjC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;SAC5B,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAE1B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;QACnD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC;gBAAE,MAAM;YAC7B,IAAI,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC;gBAAE,MAAM;QAC3D,CAAC;IACH,CAAC;IAED,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;YACrD,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;gBACjC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACpB,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC;oBAAE,MAAM;gBAC9B,IAAI,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC;oBAAE,MAAM;YAC7D,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,gBAAgB,CAAC,QAAQ,EAAoB,CAAC;AACvD,CAAC;AAED,IAAI,aAA+B,CAAC;AACpC,gBAAgB;AAChB,KAAK,UAAU,oBAAoB;IACjC,MAAM,iBAAiB,GAAwB,EAAE,CAAC;IAClD,aAAa,KAAK,IAAA,wBAAgB,EAAC,aAAa,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC;IAErC,MAAM,EAAE,uBAAuB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;IACrD,MAAM,YAAY,GAAG,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAEvE,IAAI,QAAQ;QAAE,iBAAiB,CAAC,OAAO,GAAG,QAAQ,CAAC;IACnD,IAAI,YAAY;QAAE,iBAAiB,CAAC,YAAY,GAAG,YAAY,CAAC;IAEhE,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,oBAAoB,CAAC,gBAAgC;IACzE,MAAM,iBAAiB,GAAG,MAAM,oBAAoB,EAAE,CAAC;IACvD,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,gBAAgB,CAAC;IAEzE,MAAM,gBAAgB,GAAG,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAEtD,MAAM,mBAAmB,GAAG,EAAE,GAAG,gBAAgB,EAAE,GAAG,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC;IAEvF,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC1D,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAClB,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAAE,CAAC;gBACjE,4DAA4D;gBAC5D,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,CAAC,KAAK,IAAI,gBAAgB,CAAC,EAAE,CAAC;QACjC,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC9D,CAAC;IAED,OAAO,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,SAAgB,UAAU;IACxB,MAAM,EACJ,iBAAiB,GAAG,EAAE,EACtB,sBAAsB,GAAG,EAAE,EAC3B,wBAAwB,GAAG,EAAE,EAC7B,SAAS,GAAG,EAAE,EACd,aAAa,GAAG,EAAE,EAClB,MAAM,GAAG,EAAE,EACX,+BAA+B,GAAG,EAAE,EACpC,UAAU,GAAG,EAAE,EACf,kBAAkB,GAAG,EAAE,EACvB,eAAe,GAAG,EAAE,EACpB,oBAAoB,GAAG,EAAE,EACzB,aAAa,GAAG,EAAE,EACnB,GAAG,OAAO,CAAC,GAAG,CAAC;IAEhB,MAAM,SAAS,GACb,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;IACnF,MAAM,WAAW,GAAG,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAEvC,wDAAwD;IACxD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;IAE1B,yEAAyE;IACzE,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE,CAAC;QAChD,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC9B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAI,SAAS,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE,CAAC;QAC7D,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpC,CAAC;QAED,IACE,+BAA+B,CAAC,MAAM,GAAG,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,CAAC,+BAA+B,CAAC,EAClD,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,YAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAI,WAAW,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAI,SAAS,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE,CAAC;QAC7D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,YAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,YAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAChC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAcD;;;;;;;GAOG;AACH,SAAS,cAAc;IACrB,IAAI,MAAM,IAAI,UAAU,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,OAAO,IAAI,EAAE,OAAO,EAAE,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC;QAEhG,OAAO,SAAS,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;IAChD,CAAC;IAED,IAAI,KAAK,IAAI,UAAU,EAAE,CAAC;QACxB,MAAM,OAAO,GAAG,OAAO,GAAG,EAAE,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAElF,OAAO,QAAQ,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;IAC/C,CAAC;IAED,OAAO,WAAW,OAAO,CAAC,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;AAC1D,CAAC"}