const express = require('express');
const router = express.Router();
const Vehicle = require('../models/Vehicle');

// Middleware to check if user is authenticated
const isAuthenticated = (req, res, next) => {
    if (req.isAuthenticated()) {
        return next();
    }
    res.status(401).json({ error: 'Not authenticated' });
};

// Get all vehicles for the authenticated user
router.get('/', isAuthenticated, async (req, res) => {
    try {
        const vehicles = await Vehicle.find({ userId: req.user.id });
        res.json(vehicles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch vehicles' });
    }
});

// Register a new vehicle
router.post('/', isAuthenticated, async (req, res) => {
    try {
        const { year, make, model, color, numberPlate } = req.body;
        
        // Check if vehicle with same plate exists
        const existingVehicle = await Vehicle.findOne({ numberPlate });
        if (existingVehicle) {
            return res.status(400).json({ error: 'Vehicle with this plate already exists' });
        }

        const vehicle = new Vehicle({
            userId: req.user.id,
            year,
            make,
            model,
            color,
            numberPlate
        });

        await vehicle.save();
        res.status(201).json(vehicle);
    } catch (error) {
        res.status(500).json({ error: 'Failed to register vehicle' });
    }
});

// Delete a vehicle
router.delete('/:id', isAuthenticated, async (req, res) => {
    try {
        const vehicle = await Vehicle.findOne({ 
            _id: req.params.id,
            userId: req.user.id
        });

        if (!vehicle) {
            return res.status(404).json({ error: 'Vehicle not found' });
        }

        await vehicle.deleteOne();
        res.json({ message: 'Vehicle deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete vehicle' });
    }
});

module.exports = router;