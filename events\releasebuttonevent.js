const { Events } = require('discord.js');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const startupFile = path.join(__dirname, '../data/startup.json');

const STAFF_ROLE_ID = '1237979199747129396';

// MongoDB Schema
const startupSchema = new mongoose.Schema({
    messageId: String,
    channelId: String,
    requiredReactions: Number,
    createdAt: { type: Date, default: Date.now }
});

const StartupModel = mongoose.models.Startup || mongoose.model('Startup', startupSchema);

module.exports = {
  name: Events.InteractionCreate,
  async execute(interaction) {
    if (!interaction.isButton()) return;
    if (!interaction.customId.startsWith('session_link_')) return;

    try {
      await interaction.deferReply({ ephemeral: true });

      if (!interaction.member.roles.cache.has(STAFF_ROLE_ID)) {
        await interaction.editReply({
          content: 'You do not have permission to view the session link!'
        });
        return;
      }

      let startupData = null;

      // Try MongoDB first
      if (mongoose.connection.readyState === 1) {
        try {
          startupData = await StartupModel.findOne().sort({ createdAt: -1 });
        } catch (mongoError) {
          console.error('MongoDB Error:', mongoError);
          // Fallback to JSON if MongoDB fails
          if (fs.existsSync(startupFile)) {
            try {
              startupData = JSON.parse(fs.readFileSync(startupFile, 'utf-8'));
            } catch (jsonError) {
              console.error('JSON Parse Error:', jsonError);
            }
          }
        }
      } else {
        // Use JSON if MongoDB is not connected
        if (fs.existsSync(startupFile)) {
          try {
            startupData = JSON.parse(fs.readFileSync(startupFile, 'utf-8'));
          } catch (jsonError) {
            console.error('JSON Parse Error:', jsonError);
          }
        }
      }

      // Check if startup data exists
      if (!startupData || !startupData.messageId) {
        await interaction.editReply({
          content: 'No active startup message found. Please wait for a host to use the /startup command.'
        });
        return;
      }

      try {
        const startupMessage = await interaction.channel.messages.fetch(startupData.messageId);
        
        const reaction = startupMessage.reactions.cache.get('✅');
        if (!reaction) {
          await interaction.editReply({
            content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`
          });
          return;
        }

        const userReacted = await reaction.users.fetch().then(users => users.has(interaction.user.id));
        if (!userReacted) {
          await interaction.editReply({
            content: `Please react to the startup message to have access to the session link. React [here](${startupMessage.url}).`
          });
          return;
        }

        const link = interaction.customId.replace('session_link_', '');
        await interaction.editReply({
          content: `**Session Link:** ${link}`
        });

      } catch (messageError) {
        console.error('Error fetching startup message:', messageError);
        await interaction.editReply({
          content: 'The startup message could not be found. Please wait for a host to use the /startup command.'
        });
      }

    } catch (error) {
      console.error('Error in session link button handler:', error);
      try {
        await interaction.editReply({
          content: 'An error occurred while processing your request. Please try again later.'
        });
      } catch (e) {
        console.error('Failed to send error message:', e);
      }
    }
  }
};
