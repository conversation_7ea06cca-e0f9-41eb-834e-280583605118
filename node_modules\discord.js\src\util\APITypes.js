/* eslint-disable max-len */

/**
 * @external ActivityFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ActivityFlags}
 */

/**
 * @external ActivityType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ActivityType}
 */

/**
 * @external APIActionRowComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIActionRowComponent}
 */

/**
 * @external APIApplication
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIApplication}
 */

/**
 * @external APIApplicationCommand
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIApplicationCommand}
 */

/**
 * @external APIApplicationCommandOption
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIApplicationCommandOption}
 */

/**
 * @external ApplicationIntegrationType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationIntegrationType}
 */

/**
 * @external APIAuthorizingIntegrationOwnersMap
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIAuthorizingIntegrationOwnersMap}
 */

/**
 * @external APIAutoModerationAction
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIAutoModerationAction}
 */

/**
 * @external APIButtonComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIButtonComponent}
 */

/**
 * @external APIChannel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIChannel}
 */

/**
 * @external APIChannelSelectComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIChannelSelectComponent}
 */

/**
 * @external APIEmbed
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIEmbed}
 */

/**
 * @external APIEmbedField
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIEmbedField}
 */

/**
 * @external APIEmbedProvider
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIEmbedProvider}
 */

/**
 * @external APIEmoji
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIEmoji}
 */

/**
 * @external APIGuild
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIGuild}
 */

/**
 * @external APIGuildForumDefaultReactionEmoji
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIGuildForumDefaultReactionEmoji}
 */

/**
 * @external APIGuildForumTag
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIGuildForumTag}
 */

/**
 * @external APIGuildMember
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIGuildMember}
 */

/**
 * @external APIGuildScheduledEventRecurrenceRule
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIGuildScheduledEventRecurrenceRule}
 */

/**
 * @external APIIncidentsData
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIIncidentsData}
 */

/**
 * @external APIInteraction
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIInteraction}
 */

/**
 * @external APIInteractionDataResolved
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIInteractionDataResolved}
 */

/**
 * @external APIInteractionDataResolvedChannel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIInteractionDataResolvedChannel}
 */

/**
 * @external APIInteractionDataResolvedGuildMember
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIInteractionDataResolvedGuildMember}
 */

/**
 * @external APIInteractionGuildMember
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIInteractionGuildMember}
 */

/**
 * @external APIMentionableSelectComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIMentionableSelectComponent}
 */

/**
 * @external APIMessage
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIMessage}
 */

/**
 * @external APIMessageActionRowComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIMessageActionRowComponent}
 */

/**
 * @external APIMessageComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIMessageComponent}
 */

/**
 * @external APIMessageComponentEmoji
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIMessageComponentEmoji}
 */

/**
 * @external APIMessageInteractionMetadata
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIMessageInteractionMetadata}
 */

/**
 * @external APIModalInteractionResponse
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIModalInteractionResponse}
 */

/**
 * @external APIModalInteractionResponseCallbackData
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIModalInteractionResponseCallbackData}
 */

/**
 * @external APIModalComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIModalComponent}
 */

/**
 * @external APIModalSubmission
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIModalSubmission}
 */

/**
 * @external APIOverwrite
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIOverwrite}
 */

/**
 * @external APIPartialEmoji
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIPartialEmoji}
 */

/**
 * @external APIRole
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIRole}
 */

/**
 * @external APIRoleSelectComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIRoleSelectComponent}
 */

/**
 * @external APISelectMenuOption
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APISelectMenuOption}
 */

/**
 * @external APISticker
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APISticker}
 */

/**
 * @external APIStringSelectComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIStringSelectComponent}
 */

/**
 * @external APITextInputComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APITextInputComponent}
 */

/**
 * @external APIUser
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/APIUser}
 */

/**
 * @external APIUserSelectComponent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#APIUserSelectComponent}
 */

/**
 * @external ApplicationCommandType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationCommandType}
 */

/**
 * @external ApplicationCommandOptionType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationCommandOptionType}
 */

/**
 * @external ApplicationCommandPermissionType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationCommandPermissionType}
 */

/**
 * @external ApplicationFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationFlags}
 */

/**
 * @external ApplicationRoleConnectionMetadataType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationRoleConnectionMetadataType}
 */

/**
 * @external ApplicationWebhookEventStatus
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationWebhookEventStatus}
 */

/**
 * @external ApplicationWebhookEventType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ApplicationWebhookEventType}
 */

/**
 * @external AttachmentFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AttachmentFlags}
 */

/**
 * @external AutoModerationActionType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AutoModerationActionType}
 */

/**
 * @external AutoModerationRuleEventType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AutoModerationRuleEventType}
 */

/**
 * @external AutoModerationRuleTriggerType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AutoModerationRuleTriggerType}
 */

/**
 * @external AutoModerationRuleKeywordPresetType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AutoModerationRuleKeywordPresetType}
 */

/**
 * @external AuditLogEvent
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/AuditLogEvent}
 */

/**
 * @external ButtonStyle
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ButtonStyle}
 */

/**
 * @external ChannelFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ChannelFlags}
 */

/**
 * @external ChannelType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ChannelType}
 */

/**
 * @external ComponentType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ComponentType}
 */

/**
 * @external EntitlementType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/EntitlementType}
 */

/**
 * @external ForumLayoutType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ForumLayoutType}
 */

/**
 * @external GatewayCloseCodes
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GatewayCloseCodes}
 */

/**
 * @external GatewayDispatchEvents
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GatewayDispatchEvents}
 */

/**
 * @external GatewayIntentBits
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GatewayIntentBits}
 */

/**
 * @external GatewayOpcodes
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GatewayOpcodes}
 */

/**
 * @external GatewayPresenceUpdateData
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/interface/GatewayPresenceUpdateData}
 */

/**
 * @external GuildDefaultMessageNotifications
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildDefaultMessageNotifications}
 */

/**
 * @external GuildExplicitContentFilter
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildExplicitContentFilter}
 */

/**
 * @external GuildFeature
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildFeature}
 */

/**
 * @external GuildMFALevel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildMFALevel}
 */

/**
 * @external GuildMemberFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildMemberFlags}
 */

/**
 * @external GuildNSFWLevel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildNSFWLevel}
 */

/**
 * @external GuildOnboardingMode
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildOnboardingMode}
 */

/**
 * @external GuildOnboardingPromptType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildOnboardingPromptType}
 */

/**
 * @external GuildPremiumTier
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildPremiumTier}
 */

/**
 * @external GuildScheduledEventEntityType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventEntityType}
 */

/**
 * @external GuildScheduledEventPrivacyLevel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventPrivacyLevel}
 */

/**
 * @external GuildScheduledEventRecurrenceRuleFrequency
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventRecurrenceRuleFrequency}
 */

/**
 * @external GuildScheduledEventRecurrenceRuleMonth
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventRecurrenceRuleMonth}
 */

/**
 * @external GuildScheduledEventRecurrenceRuleWeekday
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventRecurrenceRuleWeekday}
 */

/**
 * @external GuildScheduledEventStatus
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildScheduledEventStatus}
 */

/**
 * @external GuildSystemChannelFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildSystemChannelFlags}
 */

/**
 * @external GuildVerificationLevel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildVerificationLevel}
 */

/**
 * @external GuildWidgetStyle
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/GuildWidgetStyle}
 */

/**
 * @external IntegrationExpireBehavior
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/IntegrationExpireBehavior}
 */

/**
 * @external InteractionContextType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/InteractionContextType}
 */

/**
 * @external InteractionType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/InteractionType}
 */

/**
 * @external InteractionResponseType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/InteractionResponseType}
 */

/**
 * @external InviteType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/InviteType}
 */

/**
 * @external InviteTargetType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/InviteTargetType}
 */

/**
 * @external Locale
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/Locale}
 */

/**
 * @external MessageActivityType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/MessageActivityType}
 */

/**
 * @external MessageReferenceType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/MessageReferenceType}
 */

/**
 * @external MessageType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/MessageType}
 */

/**
 * @external MessageFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/MessageFlags}
 */

/**
 * @external OAuth2Scopes
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/OAuth2Scopes}
 */

/**
 * @external OverwriteType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/OverwriteType}
 */

/**
 * @external PermissionFlagsBits
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#PermissionFlagsBits}
 */

/**
 * @external PollLayoutType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/PollLayoutType}
 */

/**
 * @external ReactionType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ReactionType}
 */

/**
 * @external RoleFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/RoleFlags}
 */

/**
 * @external RESTGetAPIGuildThreadsResult
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10#RESTGetAPIGuildThreadsResult}
 */

/**
 * @external RESTJSONErrorCodes
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/RESTJSONErrorCodes}
 */

/**
 * @external SKUFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/SKUFlags}
 */

/**
 * @external SKUType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/SKUType}
 */

/**
 * @external SortOrderType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/SortOrderType}
 */

/**
 * @external StageInstancePrivacyLevel
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/StageInstancePrivacyLevel}
 */

/**
 * @external StickerType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/StickerType}
 */

/**
 * @external StickerFormatType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/StickerFormatType}
 */

/**
 * @external TeamMemberMembershipState
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/TeamMemberMembershipState}
 */

/**
 * @external TeamMemberRole
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/TeamMemberRole}
 */

/**
 * @external TextInputStyle
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/TextInputStyle}
 */

/**
 * @external ThreadAutoArchiveDuration
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/ThreadAutoArchiveDuration}
 */

/**
 * @external UserFlags
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/UserFlags}
 */

/**
 * @external VideoQualityMode
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/VideoQualityMode}
 */

/**
 * @external VoiceChannelEffectSendAnimationType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/VoiceChannelEffectSendAnimationType}
 */

/**
 * @external WebhookType
 * @see {@link https://discord-api-types.dev/api/discord-api-types-v10/enum/WebhookType}
 */
