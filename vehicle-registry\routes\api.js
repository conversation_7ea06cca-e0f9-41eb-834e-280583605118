const express = require('express');
const router = express.Router();
const Vehicle = require('../models/Vehicle');

// Middleware to check if user is authenticated
const isAuthenticated = (req, res, next) => {
    if (req.isAuthenticated()) {
        return next();
    }
    res.status(401).json({ error: 'Not authenticated' });
};

// Get all vehicles for the authenticated user
router.get('/vehicles', isAuthenticated, async (req, res) => {
    try {
        const vehicles = await Vehicle.find({ userId: req.user.id });
        res.json(vehicles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch vehicles' });
    }
});

// Get a specific vehicle
router.get('/vehicles/:vehicleId', isAuthenticated, async (req, res) => {
    try {
        const vehicle = await Vehicle.findOne({
            _id: req.params.vehicleId,
            userId: req.user.id
        });
        if (!vehicle) {
            return res.status(404).json({ error: 'Vehicle not found' });
        }
        res.json(vehicle);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch vehicle' });
    }
});

// Register a new vehicle
router.post('/vehicles', isAuthenticated, async (req, res) => {
    try {
        const { year, make, model, color, numberPlate } = req.body;
        
        const existingVehicle = await Vehicle.findOne({ numberPlate });
        if (existingVehicle) {
            return res.status(400).json({ error: 'Vehicle with this plate number already exists' });
        }

        const vehicle = new Vehicle({
            userId: req.user.id,
            year,
            make,
            model,
            color,
            numberPlate
        });

        await vehicle.save();
        res.status(201).json(vehicle);
    } catch (error) {
        res.status(500).json({ error: 'Failed to register vehicle' });
    }
});

// Update a vehicle
router.put('/vehicles/:vehicleId', isAuthenticated, async (req, res) => {
    try {
        const vehicle = await Vehicle.findOneAndUpdate(
            { _id: req.params.vehicleId, userId: req.user.id },
            req.body,
            { new: true }
        );
        if (!vehicle) {
            return res.status(404).json({ error: 'Vehicle not found' });
        }
        res.json(vehicle);
    } catch (error) {
        res.status(500).json({ error: 'Failed to update vehicle' });
    }
});

// Delete a vehicle
router.delete('/vehicles/:vehicleId', isAuthenticated, async (req, res) => {
    try {
        const vehicle = await Vehicle.findOneAndDelete({
            _id: req.params.vehicleId,
            userId: req.user.id
        });
        if (!vehicle) {
            return res.status(404).json({ error: 'Vehicle not found' });
        }
        res.json({ message: 'Vehicle deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete vehicle' });
    }
});

module.exports = router;

