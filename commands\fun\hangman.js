const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

const words = ['DISCORD', 'GAMING', 'ROLEPLAY', 'SERVER', 'COMMUNITY', 'PLAYER', 'VEHICLE', 'SESSION'];

module.exports = {
    data: new SlashCommandBuilder()
        .setName('hangman')
        .setDescription('Start a game of hangman'),
    async execute(interaction) {
        const word = words[Math.floor(Math.random() * words.length)];
        const guessed = [];
        const gameId = `${interaction.user.id}-${Date.now()}`;

        const gameData = {
            word,
            guessed,
            mistakes: 0,
            gameOver: false
        };

        if (!global.hangmanGames) {
            global.hangmanGames = new Map();
        }
        global.hangmanGames.set(gameId, gameData);

        // Using only 20 most common letters
        const letterGroups = [
            ['A', 'B', 'C', 'D', 'E'],
            ['F', 'G', 'H', 'I', 'J'],
            ['K', 'L', 'M', 'N', 'O'],
            ['P', 'Q', 'R', 'S', 'T']
        ];

        const rows = letterGroups.map(group => {
            const row = new ActionRowBuilder();
            group.forEach(letter => {
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`hangman_${gameId}_${letter}`)
                        .setLabel(letter)
                        .setStyle(ButtonStyle.Secondary)
                );
            });
            return row;
        });

        const embed = new EmbedBuilder()
            .setTitle('Hangman Game')
            .setDescription('```\n  +---+\n      |\n      |\n      |\n      |\n=========```\n' +
                'Word: ' + '_'.repeat(word.length).split('').join(' '))
            .setColor('#2B2D31')
            .setFooter({ text: `Player: ${interaction.user.tag}` });

        await interaction.reply({ embeds: [embed], components: rows });
    },
};
