const { InteractionType } = require('discord.js');
const fs = require('fs');
const path = require('path');
const sessionLinkFile = path.join(__dirname, '../../data/sessionLink.json');

module.exports = {
  name: 'releaseButton',
  execute: async (interaction) => {
    // Check if the interaction is a button press
    if (interaction.type !== InteractionType.MessageComponent) return;

    // Check for the correct custom ID
    if (interaction.customId === 'session_link') {
      // Acknowledge the interaction immediately to prevent timeout errors
      await interaction.deferUpdate(); // This prevents the "Unknown interaction" error

      // Check if the user has the staff role
      const staffRoleId = '1237979199747129396'; // Replace with your actual staff role ID
      if (!interaction.member.roles.cache.has(staffRoleId)) {
        // If the user doesn't have the staff role, deny access
        await interaction.followUp({
          content: 'You do not have permission to click on this button!',
          ephemeral: true
        });
        return;
      }

      // Read the session link from the saved file
      let sessionLink;
      try {
        sessionLink = JSON.parse(fs.readFileSync(sessionLinkFile, 'utf-8')).link;
      } catch (err) {
        // Handle if the file doesn't exist or there's an error reading it
        await interaction.followUp({
          content: 'No session link available at the moment.',
          ephemeral: true
        });
        return;
      }

      // Respond with the session link
      await interaction.followUp({
        content: `**Session Link:** ${sessionLink}`,
        ephemeral: true
      });
    }
  }
};
