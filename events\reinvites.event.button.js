const { Events } = require('discord.js');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');

// Define the path for storing the reinvites link
const DATA_PATH = path.join(__dirname, '../data/reinvites.json');

// Define MongoDB Schema
const reinvitesSchema = new mongoose.Schema({
    type: { type: String, default: 'reinvites' },
    link: String
});

const ReinvitesLink = mongoose.models.ReinvitesLink || mongoose.model('ReinvitesLink', reinvitesSchema);

// Function to load the reinvites link
async function loadReinvitesLink() {
    try {
        // Try MongoDB first
        if (mongoose.connection.readyState === 1) {
            const doc = await ReinvitesLink.findOne({ type: 'reinvites' });
            if (doc) {
                global.reinviteLink = doc.link;
                return;
            }
        }

        // Fallback to JSON
        if (fs.existsSync(DATA_PATH)) {
            const data = JSON.parse(fs.readFileSync(DATA_PATH, 'utf8'));
            global.reinviteLink = data.link;
        }
    } catch (error) {
        console.error('Error loading reinvites link:', error);
    }
}

// Function to save the reinvites link
async function saveReinvitesLink(link) {
    try {
        // Try MongoDB first
        if (mongoose.connection.readyState === 1) {
            await ReinvitesLink.findOneAndUpdate(
                { type: 'reinvites' },
                { link },
                { upsert: true }
            );
            global.reinviteLink = link;
        } else {
            // Fallback to JSON
            const dir = path.dirname(DATA_PATH);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(DATA_PATH, JSON.stringify({ link }, null, 2));
            global.reinviteLink = link;
        }
    } catch (error) {
        console.error('Error saving reinvites link:', error);
        // If MongoDB fails, try JSON as fallback
        try {
            const dir = path.dirname(DATA_PATH);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
            fs.writeFileSync(DATA_PATH, JSON.stringify({ link }, null, 2));
            global.reinviteLink = link;
        } catch (fallbackError) {
            console.error('Error in fallback save:', fallbackError);
        }
    }
}

// Load the reinvites link when the bot starts
loadReinvitesLink();

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton()) return;
        if (interaction.customId !== 'reinvites_link') return;

        try {
            await interaction.deferReply({ ephemeral: true });

            if (!global.reinviteLink) {
                return await interaction.editReply({ 
                    content: 'No re-invites link has been set yet.'
                });
            }

            await interaction.editReply({ 
                content: `**🔗 Re-invites Link:** ${global.reinviteLink}`
            });
        } catch (error) {
            console.error('Error in reinvites button handler:', error);
            try {
                await interaction.editReply({
                    content: 'An error occurred while processing your request.'
                });
            } catch (e) {
                console.error('Error sending error message:', e);
            }
        }
    }
}
