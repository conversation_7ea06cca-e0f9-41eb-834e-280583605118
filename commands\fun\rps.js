const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('rps')
        .setDescription('Play Rock, Paper, Scissors'),
    async execute(interaction) {
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`rps_${interaction.user.id}_rock`)
                    .setLabel('Rock')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🪨'),
                new ButtonBuilder()
                    .setCustomId(`rps_${interaction.user.id}_paper`)
                    .setLabel('Paper')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`rps_${interaction.user.id}_scissors`)
                    .setLabel('Scissors')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✂️')
            );

        const embed = new EmbedBuilder()
            .setTitle('Rock, Paper, Scissors')
            .setDescription('Choose your move!')
            .setColor('#2B2D31');

        await interaction.reply({ embeds: [embed], components: [row] });
    },
};
