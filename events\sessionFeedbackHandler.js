const { Events, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Define MongoDB Schema for form configuration
const formConfigSchema = new mongoose.Schema({
    type: { type: String, required: true },
    config: {
        title: String,
        fields: [{
            id: String,
            label: String,
            style: String,
            minLength: Number,
            maxLength: Number,
            placeholder: String,
            required: Boolean
        }]
    }
});

const FormConfig = mongoose.models.FormConfig || mongoose.model('FormConfig', formConfigSchema);

// Default form configuration
const defaultConfig = {
    title: 'Session Feedback',
    fields: [
        {
            id: 'host',
            label: 'Who was the host?',
            style: TextInputStyle.Short,
            minLength: 1,
            maxLength: 100,
            placeholder: 'Enter the host\'s name',
            required: true
        },
        {
            id: 'rating',
            label: 'Session Rating (1-10)',
            style: TextInputStyle.Short,
            minLength: 1,
            maxLength: 2,
            placeholder: 'Enter a number between 1 and 10',
            required: true
        },
        {
            id: 'improvement',
            label: 'What can we improve?',
            style: TextInputStyle.Paragraph,
            minLength: 1,
            maxLength: 1000,
            placeholder: 'Make the server have less FRP and must have 2+ characters',
            required: true
        },
        {
            id: 'notes',
            label: 'Additional Notes (Optional)',
            style: TextInputStyle.Paragraph,
            minLength: 1,
            maxLength: 1000,
            placeholder: '',
            required: false
        }
    ]
};

async function getFormConfig() {
    try {
        // Try to get config from MongoDB if connected
        if (mongoose.connection.readyState === 1) {
            const config = await FormConfig.findOne({ type: 'session_feedback' });
            if (config) {
                return config.config;
            }

            // If no config exists in MongoDB, create default
            const newConfig = await FormConfig.create({
                type: 'session_feedback',
                config: defaultConfig
            });
            return newConfig.config;
        }
    } catch (error) {
        console.error('❌ MongoDB error:', error);
    }

    // Fallback to JSON file
    try {
        const configPath = path.join(__dirname, '..', 'data', 'formConfig.json');
        if (fs.existsSync(configPath)) {
            return JSON.parse(fs.readFileSync(configPath, 'utf8'));
        }
        
        // If JSON doesn't exist, create it with default config
        fs.mkdirSync(path.dirname(configPath), { recursive: true });
        fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
        return defaultConfig;
    } catch (error) {
        console.error('❌ JSON fallback error:', error);
        return defaultConfig;
    }
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isButton()) return;
        if (interaction.customId !== 'session_feedback') return;
        if (!interaction.isRepliable()) return;

        try {
            // Get form configuration
            const config = await getFormConfig();

            // Create the modal
            const modal = new ModalBuilder()
                .setCustomId('session_feedback_modal')
                .setTitle(config.title);

            // Create input fields from configuration
            const actionRows = config.fields.map(field => {
                const input = new TextInputBuilder()
                    .setCustomId(field.id)
                    .setLabel(field.label)
                    .setStyle(field.style)
                    .setMinLength(field.minLength)
                    .setMaxLength(field.maxLength)
                    .setRequired(field.required);

                if (field.placeholder) {
                    input.setPlaceholder(field.placeholder);
                }

                return new ActionRowBuilder().addComponents(input);
            });

            // Add all inputs to the modal
            modal.addComponents(...actionRows);

            // Show the modal without deferring
            await interaction.showModal(modal);

        } catch (error) {
            console.error('Error in session feedback handler:', error);
            try {
                await interaction.reply({
                    content: 'There was an error processing your feedback. Please try again.',
                    ephemeral: true
                });
            } catch (err) {
                console.error('Error sending error response:', err);
            }
        }
    }
};
