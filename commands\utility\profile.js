const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require('discord.js');
const axios = require('axios');
const path = require('path');
const fs = require('fs');

const dataFolderPath = path.join(__dirname, '../../data/vehicleData');
const ticketsDirPath = path.join(__dirname, '../../data/tickets');
const licensesDirPath = path.join(__dirname, '../../data/licenses');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('profile')
        .setDescription('Displays your Roblox profile or another user\'s profile.')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Select a user to view their Roblox profile. If not selected, shows your profile.')),

    async execute(interaction) {
        try {
            const selectedUser = interaction.options.getUser('user') || interaction.user;
            const discordUserId = selectedUser.id;
            const guildId = interaction.guild.id;

            // Load vehicle and tickets data from JSON files
            const vehicleData = loadJsonFile(path.join(dataFolderPath, `${discordUserId}.json`));
            const ticketsData = loadJsonFile(path.join(ticketsDirPath, `${discordUserId}.json`));

            // Initial reply to ensure the interaction is acknowledged
            await interaction.reply({ content: 'Fetching profile information...', ephemeral: false });

            try {
                // Fetch Roblox data
                const bloxlinkApiKey = process.env.BLOXLINK;
                const bloxlinkApiUrl = `https://api.blox.link/v4/public/guilds/${guildId}/discord-to-roblox/${discordUserId}`;
                
                const bloxlinkResponse = await axios.get(bloxlinkApiUrl, {
                    headers: { 'Authorization': bloxlinkApiKey }
                });

                if (!bloxlinkResponse.data?.robloxID) {
                    await interaction.editReply({
                        content: 'No Roblox account linked. Please verify at https://blox.link/verify'
                    });
                    return;
                }

                const robloxUserId = bloxlinkResponse.data.robloxID;

                // Fetch Roblox username using Roblox API
                const robloxUserResponse = await axios.get(`https://users.roblox.com/v1/users/${robloxUserId}`);
                const robloxUsername = robloxUserResponse.data.name;

                // Fetch avatar thumbnail
                const thumbnailResponse = await axios.get(
                    `https://thumbnails.roblox.com/v1/users/avatar-headshot?userIds=${robloxUserId}&size=420x420&format=Png&isCircular=true`
                );
                const robloxProfilePicture = thumbnailResponse.data.data[0]?.imageUrl || '';

                let licenseStatus = 'Active'; // Default status
                const licenseFilePath = path.join(licensesDirPath, `${discordUserId}.json`);
                if (fs.existsSync(licenseFilePath)) {
                    const licenses = JSON.parse(fs.readFileSync(licenseFilePath, 'utf8'));
                    if (licenses.length > 0) {
                        // Get the most recent status
                        const latestLicense = licenses[licenses.length - 1];
                        licenseStatus = latestLicense.status;
                    }
                }

                const profileEmbed = new EmbedBuilder()
                    .setTitle(`${selectedUser.username}'s Profile`)
                    .setDescription(`
                        **Discord User:** <@${discordUserId}>
                        **Roblox Username:** ${robloxUsername}
                        **Roblox Profile:** [Click Here](https://www.roblox.com/users/${robloxUserId}/profile)
                        **License Status:** **${licenseStatus}**
                        **Registered Vehicles:** ${vehicleData.length}
                        **Active Tickets:** ${ticketsData.length}
                    `)
                    .setColor('#2f3136')
                    .setThumbnail(robloxProfilePicture)
                    .setTimestamp();

                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`show_registrations_${discordUserId}`)
                            .setLabel('View Registrations')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_tickets_${discordUserId}`)
                            .setLabel('View Tickets')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`show_balance_${discordUserId}`)
                            .setLabel('View Balance')
                            .setStyle(ButtonStyle.Secondary)
                    );

                await interaction.editReply({
                    content: null,
                    embeds: [profileEmbed],
                    components: [actionRow]
                });

            } catch (error) {
                console.error('API Error:', error);
                await interaction.editReply({
                    content: 'There was an error fetching your Roblox profile. Please try again later.\nIf this persists, please verify your account again at https://blox.link/verify'
                });
            }

        } catch (error) {
            console.error('Error while executing the profile command:', error);
            const errorMessage = 'An error occurred while processing your request. Please try again later.';
            
            if (interaction.replied) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};

function loadJsonFile(filePath) {
    try {
        return fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf8')) : [];
    } catch {
        return [];
    }
}
