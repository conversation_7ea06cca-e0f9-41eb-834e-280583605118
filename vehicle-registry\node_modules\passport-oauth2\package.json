{"name": "passport-oauth2", "version": "1.8.0", "description": "OAuth 2.0 authentication strategy for Passport.", "keywords": ["passport", "auth", "authn", "authentication", "authz", "authorization", "o<PERSON>h", "oauth2"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "repository": {"type": "git", "url": "git://github.com/jared<PERSON>son/passport-oauth2.git"}, "bugs": {"url": "http://github.com/jared<PERSON>son/passport-oauth2/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/jared<PERSON>son"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"base64url": "3.x.x", "oauth": "0.10.x", "passport-strategy": "1.x.x", "uid2": "0.0.x", "utils-merge": "1.x.x"}, "devDependencies": {"make-node": "0.4.6", "mocha": "2.x.x", "chai": "2.x.x", "proxyquire": "0.6.x", "chai-passport-strategy": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha --reporter spec --require test/bootstrap/node test/*.test.js test/**/*.test.js"}}