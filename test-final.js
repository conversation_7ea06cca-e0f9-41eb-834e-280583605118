/**
 * Final test of the enhanced Roblox server stats system
 */

const fetch = require("node-fetch");

// Simulate the main variables from embed5.js
const GAME_ID = "126884695634066";
let lastApiCall = 0;
const API_COOLDOWN = 30000;
let cachedStats = null;
let cacheExpiry = 0;

// Copy the functions from embed5.js for testing
function extractServerInfoFromLink(link) {
  try {
    const url = new URL(link);
    const params = new URLSearchParams(url.search);
    
    const privateServerLinkCode = params.get('privateServerLinkCode');
    const accessCode = params.get('accessCode');
    const jobId = params.get('gameInstanceId');
    
    return {
      isPrivateServer: !!(privateServerLinkCode || accessCode),
      linkCode: privateServerLinkCode || accessCode,
      jobId: jobId,
      placeId: url.pathname.split('/')[2]
    };
  } catch (error) {
    console.error("Error parsing server link:", error);
    return { isPrivateServer: false, linkCode: null, jobId: null, placeId: null };
  }
}

function getDefaultServerStats() {
  return {
    fps: "60",
    playing: "0/25",
    ping: "50ms",
    serverCount: 0,
    servers: [],
    isPrivateServer: false
  };
}

async function getPrivateServerStats(serverInfo) {
  console.log("Getting private server stats...");
  return {
    fps: "60",
    playing: "Private Server",
    ping: "~30ms",
    serverCount: "Private",
    servers: [],
    isPrivateServer: true,
    serverFound: false
  };
}

async function getRobloxServerStats(serverLink = null) {
  try {
    const now = Date.now();
    
    // Check if we have valid cached data
    if (cachedStats && now < cacheExpiry) {
      console.log("✅ Using cached server stats");
      return cachedStats;
    }
    
    // Check rate limiting
    if (now - lastApiCall < API_COOLDOWN) {
      console.log("⏳ Rate limiting: Using cached/default stats");
      return cachedStats || getDefaultServerStats();
    }
    
    // If we have a server link, try to extract info from it first
    let serverInfo = null;
    if (serverLink) {
      serverInfo = extractServerInfoFromLink(serverLink);
      
      // If it's a private server, try to get specific stats
      if (serverInfo && serverInfo.isPrivateServer) {
        console.log("🔒 Detected private server, attempting to fetch specific stats");
        const privateStats = await getPrivateServerStats(serverInfo);
        
        // Cache private server stats for 5 minutes
        cachedStats = privateStats;
        cacheExpiry = now + 300000;
        
        return privateStats;
      }
    }
    
    // Update last API call time
    lastApiCall = now;
    
    // Fetch public server stats
    console.log("🌐 Fetching public server stats...");
    const response = await fetch(`https://games.roblox.com/v1/games/${GAME_ID}/servers/Public?sortOrder=Desc&limit=10`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      console.error(`❌ Roblox API error: ${response.status} - ${response.statusText}`);
      if (response.status === 429) {
        console.log("⏳ Rate limited, extending cooldown");
        lastApiCall = now + API_COOLDOWN;
      }
      return getDefaultServerStats();
    }

    const data = await response.json();
    
    if (!data.data || data.data.length === 0) {
      console.log("⚠️ No active public servers found");
      return getDefaultServerStats();
    }

    // Calculate stats
    const servers = data.data;
    const totalPlayers = servers.reduce((sum, server) => sum + server.playing, 0);
    const maxPlayers = servers.reduce((sum, server) => sum + server.maxPlayers, 0);
    const avgPing = Math.round(servers.reduce((sum, server) => sum + (server.ping || 50), 0) / servers.length);
    const avgFps = Math.round(servers.reduce((sum, server) => sum + (server.fps || 60), 0) / servers.length);

    console.log(`✅ Successfully fetched stats: ${servers.length} servers, ${totalPlayers}/${maxPlayers} players`);

    const stats = {
      fps: avgFps.toString(),
      playing: `${totalPlayers}/${maxPlayers}`,
      ping: `${avgPing}ms`,
      serverCount: servers.length,
      servers: servers.slice(0, 5),
      isPrivateServer: false
    };
    
    // Cache the results for 2 minutes
    cachedStats = stats;
    cacheExpiry = now + 120000;
    
    return stats;

  } catch (error) {
    console.error("❌ Error fetching Roblox server stats:", error);
    return getDefaultServerStats();
  }
}

async function runFinalTest() {
  console.log("=== Final Test of Enhanced Roblox Server Stats ===\n");
  
  // Test 1: Public server
  console.log("Test 1: Public server stats");
  const publicStats = await getRobloxServerStats();
  console.log("Result:", JSON.stringify(publicStats, null, 2));
  
  // Test 2: Immediate second call (should use cache)
  console.log("\nTest 2: Immediate second call (should use cache)");
  const cachedResult = await getRobloxServerStats();
  console.log("Result:", JSON.stringify(cachedResult, null, 2));
  
  // Test 3: Private server
  console.log("\nTest 3: Private server detection");
  const privateLink = "https://www.roblox.com/games/126884695634066/Game?privateServerLinkCode=ABC123";
  const privateStats = await getRobloxServerStats(privateLink);
  console.log("Result:", JSON.stringify(privateStats, null, 2));
  
  // Test 4: Rate limiting test
  console.log("\nTest 4: Rate limiting (reset cache and API call time)");
  cachedStats = null;
  cacheExpiry = 0;
  lastApiCall = Date.now() - 1000; // 1 second ago (within cooldown)
  
  const rateLimitedResult = await getRobloxServerStats();
  console.log("Result:", JSON.stringify(rateLimitedResult, null, 2));
  
  console.log("\n=== All Tests Complete ===");
}

runFinalTest();
