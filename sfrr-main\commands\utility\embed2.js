const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('embed2')
    .setDescription('Displays server information and rules'),
  async execute(interaction) {
    const embedColor = '#2B2D31';



      const serverStartup = interaction.options.getString('server_startup');
      const image = "https://cdn.discordapp.com/attachments/1304908359262277673/1307472687437713478/Copy_of_Copy_of_j_4.png?ex=678a3114&is=6788df94&hm=592bf1302387c27991473c18de57fd7b346e62d1e54ee79e91f59bb39e5b51ce&";


    const embeds = [
      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Server Information')
        .setDescription(
          'Within this channel, you can find all the necessary information for our sessions and server within this channel. Such as peacetime, rules, session information, and more.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 1: Promotion')
        .setDescription(
          'Promoting your server within our general community is highly prohibited. If you are caught doing so you will be banned from this server.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 2: NSFW')
        .setDescription(
          'Sending any NSFW in our general channels is highly prohibited. If you are caught doing this you will be banned from our server permanently.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 3: Drama')
        .setDescription(
          'Starting any sort of drama is prohibited. If you are caught you will face consequences.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 4: Respect')
        .setDescription(
          'Respect all civilians, staff, and anyone else on this server. If you are caught disrespecting any of our staff or civilians, you will face consequences.'
        ),

      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Rule 5: Racism')
        .setDescription(
          'Racism is strictly prohibited within SFRR. If you are caught being racist you will face consequences.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Realm' }),
    ];

    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('embed2-menu')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Roleplay Information',
            description: 'View roleplay-related rules and guidelines',
            value: 'roleplay-info',
          },
          {
            label: 'Banned Vehicle List',
            description: 'View the list of banned vehicles',
            value: 'banned-vehicles',
          },
        ])
    );

    await interaction.reply({ embeds, components: [menu], ephemeral: false });
  },
};
