const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getBalance, updateBalance } = require('../../utils/economy');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('deposit')
        .setDescription('Deposit money into your bank')
        .addStringOption(option =>
            option.setName('amount')
                .setDescription('Amount to deposit (use "all" for everything)')
                .setRequired(true)),

    async execute(interaction) {
        const amount = interaction.options.getString('amount');
        const balance = await getBalance(interaction.user.id);

        let depositAmount;
        if (amount.toLowerCase() === 'all') {
            depositAmount = balance.wallet;
        } else {
            depositAmount = parseInt(amount);
            if (isNaN(depositAmount) || depositAmount <= 0) {
                return interaction.reply({
                    content: 'Please provide a valid positive number or "all".',
                    ephemeral: true
                });
            }
        }

        if (depositAmount > balance.wallet) {
            return interaction.reply({
                content: 'You don\'t have that much money in your wallet!',
                ephemeral: true
            });
        }

        await updateBalance(interaction.user.id, -depositAmount, 'wallet');
        await updateBalance(interaction.user.id, depositAmount, 'bank');

        const embed = new EmbedBuilder()
            .setColor('#2B2D31')
            .setTitle('Deposit Successful')
            .setDescription(`Deposited $${depositAmount.toLocaleString()} into your bank account.`)
            .setFooter({ text: 'Southwest Florida Roleplay Realm' });

        await interaction.reply({ embeds: [embed] });
    },
};