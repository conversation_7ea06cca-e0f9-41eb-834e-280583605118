"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitlementOwnerType = void 0;
/**
 * https://discord.com/developers/docs/resources/entitlement#create-test-entitlement
 */
var EntitlementOwnerType;
(function (EntitlementOwnerType) {
    EntitlementOwnerType[EntitlementOwnerType["Guild"] = 1] = "Guild";
    EntitlementOwnerType[EntitlementOwnerType["User"] = 2] = "User";
})(EntitlementOwnerType || (exports.EntitlementOwnerType = EntitlementOwnerType = {}));
//# sourceMappingURL=monetization.js.map