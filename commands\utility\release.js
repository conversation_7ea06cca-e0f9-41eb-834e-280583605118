const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('release')
    .setDescription('Release a session with details and a join link.')
    .addStringOption(option =>
      option.setName('peacetime')
        .setDescription('Peacetime status')
        .setRequired(true)
        .addChoices(
          { name: 'On', value: 'On' },
          { name: 'Strict', value: 'Strict' },
          { name: 'Off', value: 'Off' }
        ))
    .addStringOption(option =>
      option.setName('frp-speeds')
        .setDescription('FRP speeds status')
        .setRequired(true)
        .addChoices(
          { name: 'On', value: 'On' },
          { name: 'Strict', value: 'Strict' },
          { name: 'Off', value: 'Off' }
        ))
    .addStringOption(option =>
      option.setName('drifting-status')
        .setDescription('Drifting status')
        .setRequired(true)
        .addChoices(
          { name: 'On', value: 'On' },
          { name: 'Corners Only', value: 'Corners Only' },
          { name: 'Off', value: 'Off' }
        ))
    .addStringOption(option =>
      option.setName('link')
        .setDescription('Session link')
        .setRequired(true))
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

  async execute(interaction) {
    try {
      const staffRoleId = '1237979199747129396'; // Replace with your actual staff role ID
      const logChannelId = '1253579753621950546'; // Replace with your actual log channel ID
  
      if (!interaction.member.roles.cache.has(staffRoleId)) {
        await interaction.reply({ content: 'You do not have permission to use this command!', ephemeral: true });
        return;
      }

      await interaction.deferReply({ ephemeral: true });
      
      const peacetime = interaction.options.getString('peacetime');
      const frpSpeeds = interaction.options.getString('frp-speeds');
      const driftingStatus = interaction.options.getString('drifting-status');
      const link = interaction.options.getString('link');

      const embed = new EmbedBuilder()
        .setTitle('Southwest Florida Roleplay Synergy | Session Released')
        .setDescription(`The session has now been released! Ensure to abide by all civilian information rules and stay updated on banned vehicles. All information about the session is listed down below.

Make sure to register your vehicles in the ⁠commands channel, to register use the command </register:1360700061759049789>, to unregister use the command </unregister:1360700061956440326> and go over civilian information!
\n\n**__Session Information__**\n\nHost: ${interaction.user}\nPeacetime: ${peacetime}\nFRP-Speeds: ${frpSpeeds}\nDrifting Status: ${driftingStatus}`)
        .setColor('#35506e')
        .setImage('https://cdn.discordapp.com/attachments/893617400321290311/1361461079062089979/image.png?ex=67fed6ef&is=67fd856f&hm=1e3fe9b2c55d627c85a079dbf93f0d0e66c45822f06122c61b158f1ae36749e5&')
        .setFooter({
          text: 'Southwest Florida Roleplay Synergy',
          iconURL: 'https://cdn.discordapp.com/attachments/1360807294568239235/1360831999513989240/SFRS_Logo.png?ex=67fd35cf&is=67fbe44f&hm=aa7f448d610c6a070beafd2090192186548a92fee3ccaf221dcc0aa4f8c11dd2&'
        })
        .setTimestamp();

      const buttons = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`session_link_${link}`)
            .setLabel('Get Session Link')
            .setStyle(ButtonStyle.Secondary)
        );

      await interaction.followUp({ content: 'Session released!', ephemeral: true });

      await interaction.channel.send({
        content: `@here <@&1334707227805618244>`,
        embeds: [embed],
        components: [buttons],
        allowedMentions: { 
          parse: ['everyone'],
          roles: ['1334707227805618244']
        }
      });

    } catch (error) {
      console.error('Error in release command:', error);
      await interaction.followUp({ 
        content: 'There was an error while executing this command!', 
        ephemeral: true 
      }).catch(console.error);
    }
  }
};
