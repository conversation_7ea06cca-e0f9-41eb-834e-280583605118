const { SlashCommandBuilder } = require('@discordjs/builders');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('over')
    .setDescription('End a session')
    .addStringOption(option => 
      option.setName('start')
        .setDescription('This will tell when you started your session')
        .setRequired(true))
    .addStringOption(option => 
      option.setName('end')
        .setDescription('This will tell when you ended your session')
        .setRequired(true))
    .addStringOption(option => 
      option.setName('notes')
        .setDescription('You are able to add notes')
        .setRequired(false)),
  async execute(interaction) {
    // Check if the user has permission to use this command
    const staffRoleId = '1237979199747129396';
    if (!interaction.member.roles.cache.has(staffRoleId)) {
      return await interaction.reply({ 
        content: 'You do not have permission to use this command.', 
        ephemeral: true 
      });
    }

    // Send initial ephemeral confirmation
    await interaction.reply({ content: 'Session ended!', ephemeral: true });

    // Get the input values from the options
    const start = interaction.options.getString('start');
    const end = interaction.options.getString('end');
    const notes = interaction.options.getString('notes') || 'None';
    const logChannelId = '1253579753621950546'; // Channel ID for logging

    // Create the embed with the session details and timestamp
    const embed = new EmbedBuilder()
      .setTitle('Session Concluded')
      .setDescription(`<@${interaction.user.id}> has ended the session. Thank you all for attending the session; we appreciate everyone that joined.\n\nStart Time: ${start}\nEnd Time: ${end}\nNotes: ${notes}`)
      .setColor('#35506e')
      .setTimestamp()
      .setImage('https://cdn.discordapp.com/attachments/893617400321290311/1347421400482185301/image.png?ex=67cbc377&is=67ca71f7&hm=64b5494d356d6cedf91bea6dab104d75324e28e9a0187a81032e6a2ddf624ac9&')
      .setFooter({
        text: 'Southwest Florida Roleplay Synergy ',
        iconURL: 'https://cdn.discordapp.com/attachments/893617400321290311/1347326797192237096/image.png?ex=67cb6b5c&is=67ca19dc&hm=9ab8315707aa74d51d83cca934f3c3a2f90bd5abb7ea8f4399e4aaf99926ad28&'
      });

    const feedbackButton = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('session_feedback')
          .setLabel('Session Feedback')
          .setStyle(ButtonStyle.Secondary)
      );

    // Send the embed to the channel with the feedback button
    await interaction.channel.send({ 
      embeds: [embed],
      components: [feedbackButton]
    });

    // Log the command execution
    const logEmbed = new EmbedBuilder()
      .setTitle('Command Execution Log')
      .setDescription(`**Command:** /over\n**Executed By:** ${interaction.user.tag} (${interaction.user.id})\n**Start Time:** ${start}\n**End Time:** ${end}\n**Notes:** ${notes}`)
      .setColor('#35506e')
      .setTimestamp();

    // Send the log to the specified channel
    const logChannel = await interaction.client.channels.fetch(logChannelId);
    if (logChannel) {
      await logChannel.send({ embeds: [logEmbed] });
    } else {
      console.error(`Log channel with ID ${logChannelId} not found.`);
    }
  },
};

