<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Vehicle Registry Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #2c2f33;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background-color: #36393f;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #202225;
            background-color: #40444b;
            color: white;
        }
        button {
            background-color: #7289da;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #677bc4;
        }
        .vehicle-list {
            display: grid;
            gap: 15px;
        }
        .vehicle-item {
            background-color: #40444b;
            padding: 15px;
            border-radius: 4px;
        }
        .delete-btn {
            background-color: #ed4245;
        }
        .delete-btn:hover {
            background-color: #c03537;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vehicle Registry Dashboard</h1>
        
        <div class="card">
            <h2>Register New Vehicle</h2>
            <form id="vehicleForm">
                <div class="form-group">
                    <label for="year">Year:</label>
                    <input type="number" id="year" required>
                </div>
                <div class="form-group">
                    <label for="make">Make:</label>
                    <input type="text" id="make" required>
                </div>
                <div class="form-group">
                    <label for="model">Model:</label>
                    <input type="text" id="model" required>
                </div>
                <div class="form-group">
                    <label for="color">Color:</label>
                    <input type="text" id="color" required>
                </div>
                <div class="form-group">
                    <label for="numberPlate">Number Plate:</label>
                    <input type="text" id="numberPlate" required>
                </div>
                <button type="submit">Register Vehicle</button>
            </form>
        </div>

        <div class="card">
            <h2>Your Vehicles</h2>
            <div id="vehicleList" class="vehicle-list">
                <!-- Vehicles will be populated here -->
            </div>
        </div>
    </div>

    <script>
        document.getElementById('vehicleForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const vehicle = {
                year: document.getElementById('year').value,
                make: document.getElementById('make').value,
                model: document.getElementById('model').value,
                color: document.getElementById('color').value,
                numberPlate: document.getElementById('numberPlate').value
            };

            try {
                const response = await fetch('/api/vehicles', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(vehicle)
                });

                if (!response.ok) throw new Error('Failed to register vehicle');
                loadVehicles();
                e.target.reset();
            } catch (error) {
                alert('Error registering vehicle');
            }
        });

        async function loadVehicles() {
            try {
                const response = await fetch('/api/vehicles');
                const vehicles = await response.json();
                const vehicleList = document.getElementById('vehicleList');
                vehicleList.innerHTML = vehicles.map(vehicle => `
                    <div class="vehicle-item">
                        <h3>${vehicle.year} ${vehicle.make} ${vehicle.model}</h3>
                        <p>Color: ${vehicle.color}</p>
                        <p>Plate: ${vehicle.numberPlate}</p>
                        <button class="delete-btn" onclick="deleteVehicle('${vehicle._id}')">Unregister</button>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading vehicles:', error);
            }
        }

        async function deleteVehicle(id) {
            if (!confirm('Are you sure you want to unregister this vehicle?')) return;
            
            try {
                const response = await fetch(`/api/vehicles/${id}`, {
                    method: 'DELETE'
                });
                if (!response.ok) throw new Error('Failed to delete vehicle');
                loadVehicles();
            } catch (error) {
                alert('Error deleting vehicle');
            }
        }

        // Load vehicles when page loads
        loadVehicles();
    </script>
</body>
</html>