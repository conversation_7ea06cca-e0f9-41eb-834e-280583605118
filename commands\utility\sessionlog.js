const { SlashCommandBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');

const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('sessionlog')
        .setDescription('Log a session for staff member')
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Type of session')
                .setRequired(true)
                .addChoices(
                    { name: 'Session Host', value: 'Session Host' },
                    { name: 'Session Co-Host', value: 'Session Co-Host' }
                ))
        .addStringOption(option =>
            option.setName('date')
                .setDescription('Date of the session (YYYY-MM-DD)')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('session_start')
                .setDescription('Session start time (HH:MM)')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('session_end')
                .setDescription('Session end time (HH:MM)')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('rating')
                .setDescription('Session rating (1-10)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(10)),

    async execute(interaction) {
        try {
            // Check if user has staff role
            const staffRoleId = '1237979199747129396';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.reply({ 
                    content: 'You do not have permission to use this command.', 
                    ephemeral: true 
                });
            }

            const type = interaction.options.getString('type');
            const date = interaction.options.getString('date');
            const sessionStart = interaction.options.getString('session_start');
            const sessionEnd = interaction.options.getString('session_end');
            const rating = interaction.options.getInteger('rating');

            // Create session logs directory if it doesn't exist
            if (!fs.existsSync(sessionLogsPath)) {
                fs.mkdirSync(sessionLogsPath, { recursive: true });
            }

            const userId = interaction.user.id;
            const userLogsPath = path.join(sessionLogsPath, `${userId}.json`);
            
            // Load existing logs or create new array
            const existingLogs = loadJsonFile(userLogsPath);

            // Add new log
            const newLog = {
                type,
                date,
                sessionStart,
                sessionEnd,
                rating,
                timestamp: new Date().toISOString()
            };

            existingLogs.push(newLog);

            // Save updated logs
            fs.writeFileSync(userLogsPath, JSON.stringify(existingLogs, null, 2));

            await interaction.reply({
                content: `Session log added successfully!\nType: ${type}\nDate: ${date}\nTime: ${sessionStart} - ${sessionEnd}\nRating: ${rating}/10`,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error in sessionlog command:', error);
            await interaction.reply({
                content: 'An error occurred while logging the session. Please try again later.',
                ephemeral: true
            });
        }
    }
};

function loadJsonFile(filePath) {
    try {
        return fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf8')) : [];
    } catch {
        return [];
    }
}