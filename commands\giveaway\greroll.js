const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

const GIVEAWAYS_FILE = path.join(__dirname, '../../data/giveaways.json');

// Load giveaways from file
function loadGiveaways() {
    try {
        if (fs.existsSync(GIVEAWAYS_FILE)) {
            return JSON.parse(fs.readFileSync(GIVEAWAYS_FILE, 'utf8'));
        }
    } catch (error) {
        console.error('Error loading giveaways:', error);
    }
    return {};
}

// Save giveaways to file
function saveGiveaways(giveaways) {
    try {
        fs.writeFileSync(GIVEAWAYS_FILE, JSON.stringify(giveaways, null, 2));
    } catch (error) {
        console.error('Error saving giveaways:', error);
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('greroll')
        .setDescription('Reroll the winners of a giveaway')
        .addStringOption(option =>
            option.setName('message_id')
                .setDescription('Message ID of the giveaway to reroll')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('winners')
                .setDescription('Number of new winners to select (default: same as original)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(20)),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has('ManageMessages')) {
            return await interaction.reply({
                content: 'You need the `Manage Messages` permission to reroll giveaways.',
                ephemeral: true
            });
        }

        const messageId = interaction.options.getString('message_id');
        const newWinnerCount = interaction.options.getInteger('winners');
        
        await interaction.deferReply({ ephemeral: true });

        try {
            const giveaways = loadGiveaways();
            
            // Find giveaway by message ID
            let giveawayId = null;
            let giveaway = null;
            
            for (const [id, data] of Object.entries(giveaways)) {
                if (data.messageId === messageId) {
                    giveawayId = id;
                    giveaway = data;
                    break;
                }
            }
            
            if (!giveaway) {
                return await interaction.editReply({
                    content: 'No giveaway found with that message ID.'
                });
            }
            
            if (!giveaway.ended) {
                return await interaction.editReply({
                    content: 'This giveaway has not ended yet. Use `/gend` to end it first.'
                });
            }
            
            // Check if user is the host or has admin permissions
            if (giveaway.hostId !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
                return await interaction.editReply({
                    content: 'You can only reroll giveaways that you hosted, unless you have Administrator permissions.'
                });
            }
            
            const entries = giveaway.entries;
            if (entries.length === 0) {
                return await interaction.editReply({
                    content: 'This giveaway had no entries to reroll.'
                });
            }
            
            // Determine number of winners
            const winnersToSelect = newWinnerCount || giveaway.winners;
            
            if (winnersToSelect > entries.length) {
                return await interaction.editReply({
                    content: `Cannot select ${winnersToSelect} winners from only ${entries.length} entries.`
                });
            }
            
            // Select new random winners
            const shuffled = [...entries].sort(() => 0.5 - Math.random());
            const newWinners = shuffled.slice(0, winnersToSelect);
            
            // Get the channel and message
            const channel = await interaction.guild.channels.fetch(giveaway.channelId);
            const message = await channel.messages.fetch(giveaway.messageId);
            
            // Update embed with new winners
            const embed = new EmbedBuilder()
                .setTitle('🎉 GIVEAWAY ENDED 🎉')
                .setDescription(`**Prize:** ${giveaway.prize}\n\n🏆 **Winner${newWinners.length > 1 ? 's' : ''}:** ${newWinners.map(id => `<@${id}>`).join(', ')}\n\n**Hosted by:** <@${giveaway.hostId}>\n**Rerolled by:** ${interaction.user}`)
                .setColor('#00FF00')
                .setFooter({ text: 'Giveaway rerolled' })
                .setTimestamp();
                
            await message.edit({ embeds: [embed], components: [] });
            
            // Announce new winners
            await channel.send(`🎉 **Giveaway Rerolled!**\nNew winner${newWinners.length > 1 ? 's' : ''}: ${newWinners.map(id => `<@${id}>`).join(', ')}! You won **${giveaway.prize}**!`);
            
            await interaction.editReply({
                content: `✅ Giveaway rerolled! New winner${newWinners.length > 1 ? 's' : ''}: ${newWinners.map(id => `<@${id}>`).join(', ')}`
            });
            
        } catch (error) {
            console.error('Error rerolling giveaway:', error);
            await interaction.editReply({
                content: 'An error occurred while rerolling the giveaway. Please check the message ID and try again.'
            });
        }
    }
};
