const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON>uilder, EmbedBuilder, PermissionFlagsBits, MessageFlags } = require('discord.js');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');

// Ensure the data directory exists for JSON fallback
const dataDir = path.join(__dirname, '../../data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

const startupFile = path.join(dataDir, 'startup.json');

// MongoDB Schema
const startupSchema = new mongoose.Schema({
    messageId: String,
    channelId: String,
    requiredReactions: Number,
    createdAt: { type: Date, default: Date.now }
});

const StartupModel = mongoose.models.Startup || mongoose.model('Startup', startupSchema);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('startup')
        .setDescription('Send session startup information embeds')
        .addStringOption(option =>
            option.setName('reactions')
                .setDescription('Number of reactions needed for the session to start')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        const staffRoleId = '1237979199747129396';

        if (!interaction.member.roles.cache.has(staffRoleId)) {
            await interaction.reply({ 
                content: 'You do not have permission to use this command.', 
                flags: MessageFlags.Ephemeral 
            });
            return;
        }

        const optionReactions = interaction.options.getString('reactions');

        const embed = new EmbedBuilder()
            .setTitle('Southwest Florida Roleplay Synergy | Session Startup!')
            .setDescription(`> <@${interaction.user.id}> has now started up a session! Please ensure you have read our server information located in the information channel & make sure to register your vehicle in the channel bot commands by using the </register:1360700061759049789> command before joining.\n\n> For this session to commence, the host needs ${optionReactions} reactions.`)
            .setColor('#35506e')
            .setImage('https://cdn.discordapp.com/attachments/893617400321290311/1361460705336754176/image.png?ex=67fed696&is=67fd8516&hm=7651f9d51454086aa939c46517c03e2fda798889d4b5cf8741fe34abcfff62c2&')
            .setFooter({
                text: 'Southwest Florida Roleplay Synergy',
                iconURL: 'https://cdn.discordapp.com/attachments/1360807294568239235/1360831999513989240/SFRS_Logo.png?ex=67fd35cf&is=67fbe44f&hm=aa7f448d610c6a070beafd2090192186548a92fee3ccaf221dcc0aa4f8c11dd2&'
            });
            
        await interaction.reply({ content: 'Startup Command Executed.', ephemeral: true });
        
        try {
            const sentMessage = await interaction.channel.send({ content: '@everyone', embeds: [embed] });
            await sentMessage.react('✅');

            const startupData = {
                messageId: sentMessage.id,
                channelId: sentMessage.channel.id,
                requiredReactions: parseInt(optionReactions)
            };

            // Try MongoDB first
            if (mongoose.connection.readyState === 1) {
                try {
                    // Clear any existing startup data
                    await StartupModel.deleteMany({});
                    // Save new startup data
                    const startupDoc = new StartupModel(startupData);
                    await startupDoc.save();
                    console.log('Startup data saved to MongoDB');
                } catch (mongoError) {
                    console.error('MongoDB save error:', mongoError);
                    // Fall back to JSON if MongoDB fails
                    fs.writeFileSync(startupFile, JSON.stringify(startupData, null, 2));
                    console.log('Startup data saved to JSON (MongoDB fallback)');
                }
            } else {
                // Use JSON if MongoDB is not connected
                fs.writeFileSync(startupFile, JSON.stringify(startupData, null, 2));
                console.log('Startup data saved to JSON (MongoDB not connected)');
            }

            const logChannelId = '1253579753621950546';
            const logChannel = interaction.guild.channels.cache.get(logChannelId);
            if (logChannel) {
                const logEmbed = new EmbedBuilder()
                    .setTitle('Command Executed')
                    .setDescription(`The \`/startup\` command was executed.`)
                    .addFields(
                        { name: 'Executed by', value: `${interaction.user.tag}`, inline: true },
                        { name: 'User ID', value: `${interaction.user.id}`, inline: true },
                        { name: 'Channel', value: `${interaction.channel.name}`, inline: true },
                        { name: 'Reactions Required', value: `${optionReactions}`, inline: false },
                    )
                    .setColor('#35506e')
                    .setTimestamp();

                await logChannel.send({ embeds: [logEmbed] });
            }

        } catch (error) {
            console.error('Error in startup command:', error);
            await interaction.followUp({ 
                content: 'There was an error while executing the command.', 
                ephemeral: true 
            });
        }
    }
};
