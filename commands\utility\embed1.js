const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder } = require('discord.js');

const FOOTER_ICON = 'https://cdn.discordapp.com/attachments/893617400321290311/1287887899551076445/Screenshot_2024-09-14_150918.png?ex=66f32e93&is=66f1dd13&hm=b4b9c6b0052461ab8c67620e28ff2c7ffdece8ed2ca7055760fe046b7a20e925&';
const BANNER_IMAGE = 'https://cdn.discordapp.com/attachments/1304908359262277673/1307472687437713478/Copy_of_Copy_of_j_4.png?ex=67bc4a14&is=67baf894&hm=d9c6e0c0fc9e7eb1433c5b507741c1cf7094abaa165cbb2c53068610de043404&';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embed1')
        .setDescription('Send startup information embeds')
        .addStringOption(option =>
            option.setName('server_startup')
                .setDescription('Server startup information')
                .setRequired(true)),

    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            const serverStartup = interaction.options.getString('server_startup');

            const embeds = [
                new EmbedBuilder()
                    .setTitle(`Southwest Florida Roleplay Realm | ${serverStartup}`)
                    .setDescription(`> Welcome to our ${serverStartup} channel. Within this channel, our staff team will host sessions you can join and enjoy!\n\n> Please avoid pinging, or asking our staff team to host or do re-invites. If you are caught doing this, you will face a mute/warning. This also goes with re-invites. Our staff team will host sessions and do re-invites when they have time.`)
                    .setColor('#2B2D31'),

                new EmbedBuilder()
                    .setTitle('__**Startup Information**__')
                    .setDescription('* Leaking links that our hosts release to people who aren\'t on our server, a ban from this server is highly prohibited.\n\n* Asking for re-invites, pinging, or pinging our staff members is prohibited. Our staff team will host when they have the time.\n\n* Session cooldown is 30 minutes, Meaning if one of our hosts ends a session they would have to wait 30 minutes to host again.\n\n> To view our Banned Vehicle List you can click the button called "information" After that you can scroll down and click on the drop menu. Once you have clicked that you can find something called "Banned Vehicle List" Then you click on that. Once you have clicked on it you will be able to have access to our banned vehicle list.')
                    .setColor('#2B2D31')
                    .setFooter({
                        text: 'Southwest Florida Roleplay Realm',
                        iconURL: FOOTER_ICON
                    })
            ];

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('session_ping')
                        .setLabel('Session Ping')
                        .setStyle(ButtonStyle.Primary)
                );

            await interaction.editReply({ content: 'Sending the startup information embeds...' });
            await interaction.channel.send({ 
                files: [BANNER_IMAGE], 
                embeds, 
                components: [row] 
            });

        } catch (error) {
            console.error('Error in embed1 command:', error);
            await interaction.editReply({ 
                content: 'An error occurred while sending the embeds.',
                ephemeral: true 
            }).catch(console.error);
        }
    }
};
