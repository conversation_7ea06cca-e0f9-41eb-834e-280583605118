const { Events, Embed<PERSON><PERSON>er, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// MongoDB Schema
const embedResponseSchema = new mongoose.Schema({
    type: { type: String, required: true },
    responses: { type: Object, required: true }
});

// Create model if it doesn't exist
const EmbedResponse = mongoose.models.EmbedResponse || mongoose.model('EmbedResponse', embedResponseSchema);

// Cache management
let cachedResponses = null;
let lastCacheTime = 0;
const CACHE_TTL = 3600000; // 1 hour in milliseconds

// Default embed color
const embedColor = '#2B2D31';

// Function to fetch responses from MongoDB or fallback to local JSON
async function getResponses() {
    // Return cached responses if they exist and are not expired
    const now = Date.now();
    if (cachedResponses && (now - lastCacheTime < CACHE_TTL)) {
        return cachedResponses;
    }

    try {
        // Try to get from MongoDB
        if (mongoose.connection.readyState === 1) {
            const document = await EmbedResponse.findOne({ type: 'embed2-menu' });
            
            if (document) {
                cachedResponses = document.responses;
                lastCacheTime = now;
                return cachedResponses;
            }
            
            // If no document exists, create default responses and store them
            const defaultResponses = createDefaultResponses();
            try {
                await EmbedResponse.create({
                    type: 'embed2-menu',
                    responses: defaultResponses
                });
                console.log('✅ Created default embed2-menu responses in MongoDB');
                
                cachedResponses = defaultResponses;
                lastCacheTime = now;
                return cachedResponses;
            } catch (insertError) {
                console.error('❌ Error saving default responses to MongoDB:', insertError);
                // Fall through to JSON fallback
            }
        }
    } catch (error) {
        console.error('❌ Error accessing MongoDB:', error);
        // Fall through to JSON fallback
    }
    
    // Fallback to local JSON file
    console.log('⚠️ Using local JSON storage for embed2-menu responses');
    return loadLocalResponses();
}

// Function to load responses from local JSON file
function loadLocalResponses() {
    const localFilePath = path.join(__dirname, '..', 'data', 'embed2menu.json');
    
    try {
        if (fs.existsSync(localFilePath)) {
            const data = fs.readFileSync(localFilePath, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('❌ Error reading local embed2menu.json:', error);
    }
    
    // If file doesn't exist or can't be read, create default responses
    const defaultResponses = createDefaultResponses();
    
    try {
        const dir = path.join(__dirname, '..', 'data');
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        fs.writeFileSync(localFilePath, JSON.stringify(defaultResponses, null, 2));
        console.log('✅ Created default embed2menu.json file');
    } catch (error) {
        console.error('❌ Error saving default responses to file:', error);
    }
    
    return defaultResponses;
}

// Function to create default responses
function createDefaultResponses() {
    const roleplayEmbeds = [
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 1: Combat Logging')
            .setDescription('Combat logging is highly prohibited, such as leaving the game to avoid punishment from staff members or law enforcement. If you are caught doing this, you will face a high consequence.')
            .toJSON(),
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 2: Voiding Scenes')
            .setDescription('Voiding a scene without staff permission is prohibited. If you are caught doing this, you will face consequences. (Only staff can void scenes)')
            .toJSON(),
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 3: Excessive Honking')
            .setDescription('You will be kicked if you are caught abusing your car\'s horn. Any honking longer than 5 seconds will result in consequences.')
            .toJSON(),
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 4: Following the Speed Limit')
            .setDescription('You should follow the speed limit at all times. If you are caught speeding, you will be pulled over by a law enforcement officer.')
            .toJSON(),
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 5: Yielding for Law Enforcement')
            .setDescription('You must yield to Law Enforcement if they have their lights and sirens on. Failure to do so will result in being kicked from the session.')
            .toJSON(),
        new EmbedBuilder()
            .setColor(embedColor)
            .setTitle('Rule 6: Peacetime Information')
            .setDescription(
                '**Normal Peacetime:**\n' +
                '- Do not go over 80 MPH.\n' +
                '- Do not rob any places or be on the criminal team.\n' +
                '- Do not run any red lights.\n' +
                '- Do not run from Staff or Law Enforcement.\n\n' +
                '**Strict Peacetime:**\n' +
                '- Do not go over 70 MPH.\n' +
                '- No off-roading.\n' +
                '- No running red lights.\n' +
                '- No running from Staff or Law Enforcement.\n' +
                '- No drifting or donuts.\n' +
                '- No robberies or criminal team activities.\n' +
                '- Drive responsibly.\n\n' +
                '**Peacetime Off:**\n' +
                '- You can exceed 85 MPH.\n' +
                '- You can rob stores.\n' +
                '- You can drift.\n' +
                '- You can evade Law Enforcement.'
            )
            .setFooter({ text: 'Southwest Florida Roleplay Realm' })
            .toJSON()
    ];

    const bannedVehicleButton = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setLabel('View Banned Vehicle List')
            .setStyle(ButtonStyle.Link)
            .setURL('https://docs.google.com/document/d/1MaE7Owc7_lodumohkmZI2NrFyqIt9oT8TNGIOarOXuo/edit?usp=sharing')
    ).toJSON();

    return {
        'roleplay-info': { embeds: roleplayEmbeds },
        'banned-vehicles': { 
            content: 'Click the button below to view the banned vehicle list:', 
            components: [bannedVehicleButton] 
        },
        'peacetime-rules': {
            embeds: [roleplayEmbeds[5]] // Just the peacetime information embed
        }
    };
}

// Function to convert stored JSON to Discord.js objects
function prepareResponse(responseData, option) {
    const response = { ...responseData[option] };
    
    if (response.embeds) {
        response.embeds = response.embeds.map(embedData => {
            return EmbedBuilder.from(embedData);
        });
    }
    
    if (response.components) {
        response.components = response.components.map(rowData => {
            return ActionRowBuilder.from(rowData);
        });
    }
    
    return response;
}

// Function to update responses in MongoDB
async function updateResponses(newResponses) {
    if (mongoose.connection.readyState === 1) {
        try {
            await EmbedResponse.findOneAndUpdate(
                { type: 'embed2-menu' },
                { responses: newResponses },
                { upsert: true }
            );
            cachedResponses = newResponses;
            lastCacheTime = Date.now();
            return true;
        } catch (error) {
            console.error('❌ Error updating responses in MongoDB:', error);
            return false;
        }
    }
    return false;
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'embed2-menu') return;

        try {
            const selectedOption = interaction.values[0];
            const responses = await getResponses();
            
            if (!responses[selectedOption]) {
                return await interaction.reply({
                    content: 'Invalid selection. Please try again.',
                    ephemeral: true
                });
            }
            
            const responseData = prepareResponse(responses, selectedOption);
            console.log(`User ${interaction.user.tag} selected option: ${selectedOption}`);
            
            await interaction.reply({
                ...responseData,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error in embed2menuevent:', error);
            await interaction.reply({
                content: 'An error occurred while processing your request.',
                ephemeral: true
            }).catch(e => console.error('Error sending error message:', e));
        }
    },
};
