require('dotenv').config({ path: './sfrr-main/.env' });
const { token } = process.env;
const { Client, Collection, GatewayIntentBits } = require("discord.js");
const fs = require("fs");
const { REST } = require("@discordjs/rest");
const { Routes } = require("discord-api-types/v9");
const path = require('path');
const spamHandler = require('./events/spamHandler');
const { initializeEconomy } = require('./utils/economy');
const mongoose = require('mongoose');
const mongoURL = process.env.MONGODB_URI;

// Add these lines - replace with your actual IDs
const clientId = process.env.CLIENT_ID || "1242613284549558402"; // Your bot's client ID
const guildId = process.env.GUILD_ID || "1237978330079432734";   // Your Discord server ID

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
  ],
});

// Initialize storage mode global variable
global.storageMode = 'mongodb'; // Will be changed to 'json' if MongoDB fails

// Connect to MongoDB with fallback handling
const connectToMongoDB = async () => {
  try {
    await mongoose.connect(mongoURL);
    console.log('✅ Connected to MongoDB database');
    global.storageMode = 'mongodb';
    return true;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    console.log('⚠️ Falling back to JSON storage');
    global.storageMode = 'json';
    return false;
  }
};

// Create MongoDB models
const createModels = () => {
  try {
    // Economy model
    const economySchema = new mongoose.Schema({
      userId: { type: String, required: true, unique: true },
      balance: { type: Number, default: 0 }
    });

    // Reinvites model
    const reinvitesSchema = new mongoose.Schema({
      type: { type: String, default: 'reinvites' },
      link: String
    });

    // Ticket model
    const ticketSchema = new mongoose.Schema({
      userId: { type: String, required: true, unique: true },
      tickets: { type: Array, default: [] }
    });

    // Attach models to client
    client.models = {
      Economy: mongoose.models.Economy || mongoose.model('Economy', economySchema),
      Vehicle: require('./models/Vehicle'), // Import the Vehicle model instead of redefining it
      Ticket: mongoose.models.Ticket || mongoose.model('Ticket', ticketSchema),
      Reinvites: mongoose.models.Reinvites || mongoose.model('Reinvites', reinvitesSchema)
    };
    
    console.log('✅ MongoDB models created');
  } catch (error) {
    console.error('❌ Error creating MongoDB models:', error);
  }
};

// Initialize data storage system
const initializeStorage = async () => {
  const mongoConnected = await connectToMongoDB();
  createModels(); // Create models regardless of connection status

  if (!mongoConnected) {
    // Ensure JSON storage directories exist
    const directories = [
      path.join(__dirname, 'data'),
      path.join(__dirname, 'data', 'vehicleData'),
      path.join(__dirname, 'data', 'tickets'),
      path.join(__dirname, 'data', 'economy')
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  return mongoConnected;
};

// Handle Events
const handleEvents = async () => {
  const eventFiles = fs.readdirSync(`./events`).filter((file) => file.endsWith(".js"));
  for (const file of eventFiles) {
    const event = require(`./events/${file}`);
    if (event.once) {
      client.once(event.name, (...args) => event.execute(...args, client));
    } else {
      client.on(event.name, (...args) => event.execute(...args, client));
    }
  }
};

// Handle Commands
const loadCommands = async () => {
  const commandFolders = fs.readdirSync("./commands");
  const commandNames = new Set(); // Track command names

  client.commands = new Collection();
  client.commandArray = [];

  for (const folder of commandFolders) {
    const folderPath = `./commands/${folder}`;
    
    // Check if it's a directory or a file
    const stats = fs.statSync(folderPath);
    if (stats.isDirectory()) {
      const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith(".js"));
      for (const file of commandFiles) {
        try {
          const command = require(`${folderPath}/${file}`);
          if (!command.data || typeof command.data.toJSON !== "function") {
            throw new TypeError(`Command file ${folder}/${file} is invalid.`);
          }
          
          // Check for duplicate command names
          const commandName = command.data.name;
          if (commandNames.has(commandName)) {
            console.error(`Duplicate command name found: ${commandName} in ${folder}/${file}`);
            continue; // Skip this command
          }
          
          commandNames.add(commandName);
          client.commands.set(command.data.name, command);
          client.commandArray.push(command.data.toJSON());
          console.log(`Loaded command: ${commandName} from ${folder}/${file}`);
        } catch (error) {
          console.error(`Error in command file ${folder}/${file}:`, error);
        }
      }
    } else if (folder.endsWith(".js")) {
      try {
        const command = require(`./commands/${folder}`);
        if (!command.data || typeof command.data.toJSON !== "function") {
          throw new TypeError(`Command file ${folder} is invalid.`);
        }

        // Check for duplicate command names
        const commandName = command.data.name;
        if (commandNames.has(commandName)) {
          console.error(`Duplicate command name found: ${commandName} in ${folder}`);
          continue; // Skip this command
        }

        commandNames.add(commandName);
        client.commands.set(command.data.name, command);
        client.commandArray.push(command.data.toJSON());
        console.log(`Loaded command: ${commandName} from ${folder}`);
      } catch (error) {
        console.error(`Error in command file ${folder}:`, error);
      }
    }
  }

  // Register Commands with Discord
  const rest = new REST({ version: "9" }).setToken(token);
  try {
    await rest.put(Routes.applicationGuildCommands(clientId, guildId), {
      body: client.commandArray,
    });
    console.log("✅ Slash commands registered successfully.");
  } catch (error) {
    console.error("❌ Error registering commands:", error);
  }
};

// Migrate existing JSON data to MongoDB
const migrateDataToMongoDB = async () => {
  try {
    console.log('Starting data migration to MongoDB...');
    
    // Migrate vehicle data
    const vehicleDataPath = path.join(__dirname, 'data', 'vehicleData');
    if (fs.existsSync(vehicleDataPath)) {
      const files = fs.readdirSync(vehicleDataPath).filter(file => file.endsWith('.json'));
      for (const file of files) {
        try {
          const userId = file.replace('.json', '');
          const data = fs.readFileSync(path.join(vehicleDataPath, file), 'utf8');
          const vehicles = JSON.parse(data);
          
          await client.models.Vehicle.findOneAndUpdate(
            { userId },
            { userId, vehicles },
            { upsert: true }
          );
        } catch (error) {
          console.error(`Error migrating vehicle data from ${file}:`, error);
        }
      }
    }
    
    // Migrate tickets data
    const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
    if (fs.existsSync(ticketsDataPath)) {
      const files = fs.readdirSync(ticketsDataPath).filter(file => file.endsWith('.json'));
      for (const file of files) {
        try {
          const userId = file.replace('.json', '');
          const data = fs.readFileSync(path.join(ticketsDataPath, file), 'utf8');
          const tickets = JSON.parse(data);
          
          await client.models.Ticket.findOneAndUpdate(
            { userId },
            { userId, tickets },
            { upsert: true }
          );
        } catch (error) {
          console.error(`Error migrating tickets data from ${file}:`, error);
        }
      }
    }
    
    console.log('✅ Data migration to MongoDB completed');
  } catch (error) {
    console.error('❌ Error during data migration:', error);
  }
};

// Load Vehicle Data
const loadVehicleData = async () => {
  if (global.storageMode === 'mongodb') {
    try {
      // MongoDB loading logic (if needed)
      console.log('✅ Vehicle data loaded from MongoDB');
    } catch (error) {
      console.error('❌ Error loading vehicle data from MongoDB:', error);
    }
  } else {
    try {
      const vehicleDataPath = path.join(__dirname, 'data', 'vehicleData');
      if (!fs.existsSync(vehicleDataPath)) {
        fs.mkdirSync(vehicleDataPath, { recursive: true });
      }
      console.log('✅ Vehicle data directory ready for JSON storage');
    } catch (error) {
      console.error('❌ Error preparing vehicle data directory:', error);
    }
  }
};

// Load Tickets Data
const loadTicketsData = async () => {
  if (global.storageMode === 'mongodb') {
    try {
      // MongoDB loading logic (if needed)
      console.log('✅ Tickets data loaded from MongoDB');
    } catch (error) {
      console.error('❌ Error loading tickets data from MongoDB:', error);
    }
  } else {
    try {
      const ticketsDataPath = path.join(__dirname, 'data', 'tickets');
      if (!fs.existsSync(ticketsDataPath)) {
        fs.mkdirSync(ticketsDataPath, { recursive: true });
      }
      console.log('✅ Tickets data directory ready for JSON storage');
    } catch (error) {
      console.error('❌ Error preparing tickets data directory:', error);
    }
  }
};

// Initialize the bot
(async () => {
  try {
    // Initialize storage system first
    await initializeStorage();
    
    // Set up global variables
    global.tictactoeGames = new Map();
    global.cooldowns = new Map();

    // Load data based on storage mode
    if (global.storageMode === 'mongodb') {
      console.log('📦 Using MongoDB as primary storage');
      await loadVehicleData();
      await loadTicketsData();
      await initializeEconomy();
    } else {
      console.log('📁 Using JSON as fallback storage');
      await loadVehicleData();
      await loadTicketsData();
      await initializeEconomy();
    }

    // Initialize bot features
    await handleEvents();
    await loadCommands();
    spamHandler(client);
    
    // Login to Discord
    await client.login(token);
    console.log('✅ Bot is online!');
    
  } catch (error) {
    console.error("❌ Error during bot initialization:", error);
    process.exit(1); // Exit only if critical bot initialization fails
  }
})();

// Export client for use in other files
module.exports = client;
