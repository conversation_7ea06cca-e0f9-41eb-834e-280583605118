const { InteractionType } = require('discord.js');
const { MongoClient } = require('mongodb');
require('dotenv').config();

// MongoDB connection URI from environment variables
const uri = process.env.MONGODB_URI;
const dbName = process.env.DB_NAME || 'sfrr';
const collectionName = 'earlyAccessLinks';

module.exports = {
  name: 'interactionCreate',
  async execute(interaction) {
    if (!interaction.isButton() || interaction.customId !== 'early_access_link') return;

    try {
      const staffRoleId = '1237979199747129396';
      const earlyAccessRoleId1 = '1281003485994942558';
      const earlyAccessRoleId2 = '1281002681879756955';

      if (!interaction.member.roles.cache.has(staffRoleId) &&
          !interaction.member.roles.cache.has(earlyAccessRoleId1) &&
          !interaction.member.roles.cache.has(earlyAccessRoleId2)) {
        return await interaction.reply({
          content: 'You do not have permission to click on this button!',
          ephemeral: true
        });
      }

      // Retrieve the link from MongoDB
      let link = null;
      try {
        const client = new MongoClient(uri);
        await client.connect();
        
        const db = client.db(dbName);
        const collection = db.collection(collectionName);
        
        // Find the document with the message ID
        const document = await collection.findOne({ messageId: interaction.message.id });
        
        if (document) {
          link = document.link;
        }
        
        await client.close();
      } catch (error) {
        console.error('Error retrieving link from MongoDB:', error);
        return await interaction.reply({
          content: 'Unable to retrieve the early access link.',
          ephemeral: true
        });
      }

      if (!link) {
        return await interaction.reply({
          content: 'The early access link is no longer available.',
          ephemeral: true
        });
      }

      await interaction.reply({
        content: `**Early-Access:** ${link}`,
        ephemeral: true
      });

    } catch (error) {
      console.error('Error handling button interaction:', error);
      try {
        await interaction.reply({
          content: 'An error occurred while processing your request.',
          ephemeral: true
        });
      } catch (replyError) {
        console.error('Error sending reply:', replyError);
      }
    }
  }
};
