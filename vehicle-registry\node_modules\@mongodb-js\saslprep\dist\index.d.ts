import type { createMemoryCodePoints } from './memory-code-points';
declare function saslprep({ unassigned_code_points, commonly_mapped_to_nothing, non_ASCII_space_characters, prohibited_characters, bidirectional_r_al, bidirectional_l, }: ReturnType<typeof createMemoryCodePoints>, input: string, opts?: {
    allowUnassigned?: boolean;
}): string;
declare namespace saslprep {
    export var saslprep: typeof import(".");
    var _a: typeof import(".");
    export { _a as default };
}
export = saslprep;
//# sourceMappingURL=index.d.ts.map