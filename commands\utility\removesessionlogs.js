const { SlashCommandBuilder } = require('discord.js');
const path = require('path');
const fs = require('fs');

const sessionLogsPath = path.join(__dirname, '../../data/sessionLogs');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('removesessionlogs')
        .setDescription('Remove all session logs for a user')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('User to remove session logs for')
                .setRequired(true)),

    async execute(interaction) {
        try {
            // Check if user has staff role
            const staffRoleId = '1237979199747129396';
            if (!interaction.member.roles.cache.has(staffRoleId)) {
                return await interaction.reply({ 
                    content: 'You do not have permission to use this command.', 
                    ephemeral: true 
                });
            }

            const targetUser = interaction.options.getUser('user');
            const userLogsPath = path.join(sessionLogsPath, `${targetUser.id}.json`);

            if (!fs.existsSync(userLogsPath)) {
                return await interaction.reply({
                    content: `No session logs found for ${targetUser.username}.`,
                    ephemeral: true
                });
            }

            // Remove the logs file
            fs.unlinkSync(userLogsPath);

            await interaction.reply({
                content: `Successfully removed all session logs for ${targetUser.username}.`,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error in removesessionlogs command:', error);
            await interaction.reply({
                content: 'An error occurred while removing session logs. Please try again later.',
                ephemeral: true
            });
        }
    }
};