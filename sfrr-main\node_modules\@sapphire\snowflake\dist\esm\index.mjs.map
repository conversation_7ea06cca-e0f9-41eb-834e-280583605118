{"version": 3, "sources": ["../../src/lib/Snowflake.ts", "../../src/lib/DiscordSnowflake.ts", "../../src/lib/TwitterSnowflake.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,IAAM,kBAAkB,OAAO,+BAA+B;AAC9D,IAAM,cAAc,OAAO,2BAA2B;AACtD,IAAM,kBAAkB,OAAO,+BAA+B;AAC9D,IAAM,iBAAiB,OAAO,8BAA8B;AAKrD,IAAM,kBAAkB;AAKxB,IAAM,mBAAmB;AAKzB,IAAM,mBAAmB;AAlBhC;AAiCO,IAAM,aAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA,EAkCf,YAAY,OAA+B;AA7BlD;AAAA;AAAA;AAAA;AAAA,wBAAO,UAAS,KAAK;AAMrB;AAAA;AAAA;AAAA;AAAA,wBAAkB;AAMlB;AAAA;AAAA;AAAA;AAAA,wBAAS,IAAmB;AAM5B;AAAA;AAAA;AAAA;AAAA,wBAAS,IAAmB;AAM5B;AAAA;AAAA;AAAA;AAAA,wBAAS,IAAkB;AAM1B,SAAK,WAAW,IAAI,OAAO,iBAAiB,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,QAAgB;AAC1B,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,YAAoB;AAC9B,WAAO,KAAK,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,UAAU,OAAwB;AAC5C,SAAK,eAAe,IAAI,OAAO,KAAK,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,IAAW,WAAmB;AAC7B,WAAO,KAAK,cAAc;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,SAAS,OAAwB;AAC3C,SAAK,cAAc,IAAI,OAAO,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcO,SAAS;AAAA,IACf;AAAA,IACA,YAAY,KAAK,IAAI;AAAA,IACrB,WAAW,KAAK,cAAc;AAAA,IAC9B,YAAY,KAAK,eAAe;AAAA,EACjC,IAA8B,CAAC,GAAG;AACjC,QAAI,qBAAqB;AAAM,kBAAY,OAAO,UAAU,QAAQ,CAAC;AAAA,aAC5D,OAAO,cAAc;AAAU,kBAAY,OAAO,SAAS;AAAA,aAC3D,OAAO,cAAc,UAAU;AACvC,YAAM,IAAI,UAAU,oEAAoE,OAAO,SAAS,GAAG;AAAA,IAC5G;AAEA,QAAI,OAAO,cAAc,UAAU;AAClC,kBAAY,KAAK,eAAe;AAChC,WAAK,eAAe,IAAK,YAAY,KAAM;AAAA,IAC5C;AAGA,WACG,YAAY,KAAK,WAAW,KAAM,OAClC,WAAW,oBAAoB,OAC/B,YAAY,qBAAqB,MAClC,YAAY;AAAA,EAEf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYO,YAAY,IAA6C;AAC/D,UAAM,WAAW,OAAO,EAAE;AAC1B,UAAM,QAAQ,KAAK,WAAW;AAC9B,WAAO;AAAA,MACN,IAAI;AAAA,MACJ,YAAY,YAAY,OAAO;AAAA,MAC/B,UAAW,YAAY,MAAO;AAAA,MAC9B,WAAY,YAAY,MAAO;AAAA,MAC/B,WAAW,WAAW;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,cAAc,IAA6B;AACjD,WAAO,QAAQ,OAAO,EAAE,KAAK,OAAO,KAAK,WAAW,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OAAc,QAAQ,GAAoB,GAAgC;AACzE,UAAM,QAAQ,OAAO;AACrB,WAAO,UAAU,OAAO,IACrB,UAAU,WACT,UAAU,GAAa,CAAW,IAClC,UAAU,GAAa,CAAW,IACnC,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EAClC;AACD;AAjKmB,kBAMT,sBAMA,sBAMA;AA7Ba;AAAhB,IAAM,YAAN;AA+KP,SAAS,UAAU,GAAW,GAAW;AACxC,SAAO,MAAM,IAAI,IAAI,IAAI,IAAI,KAAK;AACnC;AAFS;AAKT,SAAS,UAAU,GAAW,GAAW;AACxC,SAAO,MAAM,IAAI,IAAI,EAAE,SAAS,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE,SAAS,IAAI,IAAI,IAAI,KAAK;AACxF;AAFS;;;AC9MF,IAAM,mBAAmB,IAAI,UAAU,cAAc;;;ACArD,IAAM,mBAAmB,IAAI,UAAU,cAAc", "sourcesContent": ["const IncrementSymbol = Symbol('@sapphire/snowflake.increment');\nconst EpochSymbol = Symbol('@sapphire/snowflake.epoch');\nconst ProcessIdSymbol = Symbol('@sapphire/snowflake.processId');\nconst WorkerIdSymbol = Symbol('@sapphire/snowflake.workerId');\n\n/**\n * The maximum value the `workerId` field accepts in snowflakes.\n */\nexport const MaximumWorkerId = 0b11111n;\n\n/**\n * The maximum value the `processId` field accepts in snowflakes.\n */\nexport const MaximumProcessId = 0b11111n;\n\n/**\n * The maximum value the `increment` field accepts in snowflakes.\n */\nexport const MaximumIncrement = 0b111111111111n;\n\n/**\n * A class for generating and deconstructing Twitter snowflakes.\n *\n * A {@link https://developer.twitter.com/en/docs/twitter-ids Twitter snowflake}\n * is a 64-bit unsigned integer with 4 fields that have a fixed epoch value.\n *\n * If we have a snowflake `266241948824764416` we can represent it as binary:\n * ```\n * 64                                          22     17     12          0\n *  000000111011000111100001101001000101000000  00001  00000  000000000000\n *           number of ms since epoch           worker  pid    increment\n * ```\n */\nexport class Snowflake {\n\t/**\n\t * Alias for {@link deconstruct}\n\t */\n\t// eslint-disable-next-line @typescript-eslint/unbound-method\n\tpublic decode = this.deconstruct;\n\n\t/**\n\t * Internal reference of the epoch passed in the constructor\n\t * @internal\n\t */\n\tprivate readonly [EpochSymbol]: bigint;\n\n\t/**\n\t * Internal incrementor for generating snowflakes\n\t * @internal\n\t */\n\tprivate [IncrementSymbol] = 0n;\n\n\t/**\n\t * The process ID that will be used by default in the generate method\n\t * @internal\n\t */\n\tprivate [ProcessIdSymbol] = 1n;\n\n\t/**\n\t * The worker ID that will be used by default in the generate method\n\t * @internal\n\t */\n\tprivate [WorkerIdSymbol] = 0n;\n\n\t/**\n\t * @param epoch the epoch to use\n\t */\n\tpublic constructor(epoch: number | bigint | Date) {\n\t\tthis[EpochSymbol] = BigInt(epoch instanceof Date ? epoch.getTime() : epoch);\n\t}\n\n\t/**\n\t * The epoch for this snowflake\n\t */\n\tpublic get epoch(): bigint {\n\t\treturn this[EpochSymbol];\n\t}\n\n\t/**\n\t * Gets the configured process ID\n\t */\n\tpublic get processId(): bigint {\n\t\treturn this[ProcessIdSymbol];\n\t}\n\n\t/**\n\t * Sets the process ID that will be used by default for the {@link generate} method\n\t * @param value The new value, will be coerced to BigInt and masked with `0b11111n`\n\t */\n\tpublic set processId(value: number | bigint) {\n\t\tthis[ProcessIdSymbol] = BigInt(value) & MaximumProcessId;\n\t}\n\n\t/**\n\t * Gets the configured worker ID\n\t */\n\tpublic get workerId(): bigint {\n\t\treturn this[WorkerIdSymbol];\n\t}\n\n\t/**\n\t * Sets the worker ID that will be used by default for the {@link generate} method\n\t * @param value The new value, will be coerced to BigInt and masked with `0b11111n`\n\t */\n\tpublic set workerId(value: number | bigint) {\n\t\tthis[WorkerIdSymbol] = BigInt(value) & MaximumWorkerId;\n\t}\n\n\t/**\n\t * Generates a snowflake given an epoch and optionally a timestamp\n\t * @param options options to pass into the generator, see {@link SnowflakeGenerateOptions}\n\t *\n\t * **note** when `increment` is not provided it defaults to the private `increment` of the instance\n\t * @example\n\t * ```typescript\n\t * const epoch = new Date('2000-01-01T00:00:00.000Z');\n\t * const snowflake = new Snowflake(epoch).generate();\n\t * ```\n\t * @returns A unique snowflake\n\t */\n\tpublic generate({\n\t\tincrement,\n\t\ttimestamp = Date.now(),\n\t\tworkerId = this[WorkerIdSymbol],\n\t\tprocessId = this[ProcessIdSymbol]\n\t}: SnowflakeGenerateOptions = {}) {\n\t\tif (timestamp instanceof Date) timestamp = BigInt(timestamp.getTime());\n\t\telse if (typeof timestamp === 'number') timestamp = BigInt(timestamp);\n\t\telse if (typeof timestamp !== 'bigint') {\n\t\t\tthrow new TypeError(`\"timestamp\" argument must be a number, bigint, or Date (received ${typeof timestamp})`);\n\t\t}\n\n\t\tif (typeof increment !== 'bigint') {\n\t\t\tincrement = this[IncrementSymbol];\n\t\t\tthis[IncrementSymbol] = (increment + 1n) & MaximumIncrement;\n\t\t}\n\n\t\t// timestamp, workerId, processId, increment\n\t\treturn (\n\t\t\t((timestamp - this[EpochSymbol]) << 22n) |\n\t\t\t((workerId & MaximumWorkerId) << 17n) |\n\t\t\t((processId & MaximumProcessId) << 12n) |\n\t\t\t(increment & MaximumIncrement)\n\t\t);\n\t}\n\n\t/**\n\t * Deconstructs a snowflake given a snowflake ID\n\t * @param id the snowflake to deconstruct\n\t * @returns a deconstructed snowflake\n\t * @example\n\t * ```typescript\n\t * const epoch = new Date('2000-01-01T00:00:00.000Z');\n\t * const snowflake = new Snowflake(epoch).deconstruct('3971046231244935168');\n\t * ```\n\t */\n\tpublic deconstruct(id: string | bigint): DeconstructedSnowflake {\n\t\tconst bigIntId = BigInt(id);\n\t\tconst epoch = this[EpochSymbol];\n\t\treturn {\n\t\t\tid: bigIntId,\n\t\t\ttimestamp: (bigIntId >> 22n) + epoch,\n\t\t\tworkerId: (bigIntId >> 17n) & MaximumWorkerId,\n\t\t\tprocessId: (bigIntId >> 12n) & MaximumProcessId,\n\t\t\tincrement: bigIntId & MaximumIncrement,\n\t\t\tepoch\n\t\t};\n\t}\n\n\t/**\n\t * Retrieves the timestamp field's value from a snowflake.\n\t * @param id The snowflake to get the timestamp value from.\n\t * @returns The UNIX timestamp that is stored in `id`.\n\t */\n\tpublic timestampFrom(id: string | bigint): number {\n\t\treturn Number((BigInt(id) >> 22n) + this[EpochSymbol]);\n\t}\n\n\t/**\n\t * Returns a number indicating whether a reference snowflake comes before, or after, or is same as the given\n\t * snowflake in sort order.\n\t * @param a The first snowflake to compare.\n\t * @param b The second snowflake to compare.\n\t * @returns `-1` if `a` is older than `b`, `0` if `a` and `b` are equals, `1` if `a` is newer than `b`.\n\t * @example Sort snowflakes in ascending order\n\t * ```typescript\n\t * const ids = ['737141877803057244', '1056191128120082432', '254360814063058944'];\n\t * console.log(ids.sort((a, b) => Snowflake.compare(a, b)));\n\t * // → ['254360814063058944', '737141877803057244', '1056191128120082432'];\n\t * ```\n\t * @example Sort snowflakes in descending order\n\t * ```typescript\n\t * const ids = ['737141877803057244', '1056191128120082432', '254360814063058944'];\n\t * console.log(ids.sort((a, b) => -Snowflake.compare(a, b)));\n\t * // → ['1056191128120082432', '737141877803057244', '254360814063058944'];\n\t * ```\n\t */\n\tpublic static compare(a: string | bigint, b: string | bigint): -1 | 0 | 1 {\n\t\tconst typeA = typeof a;\n\t\treturn typeA === typeof b\n\t\t\t? typeA === 'string'\n\t\t\t\t? cmpString(a as string, b as string)\n\t\t\t\t: cmpBigInt(a as bigint, b as bigint)\n\t\t\t: cmpBigInt(BigInt(a), BigInt(b));\n\t}\n}\n\n/** @internal */\nfunction cmpBigInt(a: bigint, b: bigint) {\n\treturn a === b ? 0 : a < b ? -1 : 1;\n}\n\n/** @internal */\nfunction cmpString(a: string, b: string) {\n\treturn a === b ? 0 : a.length < b.length ? -1 : a.length > b.length ? 1 : a < b ? -1 : 1;\n}\n\n/**\n * Options for Snowflake#generate\n */\nexport interface SnowflakeGenerateOptions {\n\t/**\n\t * Timestamp or date of the snowflake to generate\n\t * @default Date.now()\n\t */\n\ttimestamp?: number | bigint | Date;\n\n\t/**\n\t * The increment to use\n\t * @default 0n\n\t * @remark keep in mind that this bigint is auto-incremented between generate calls\n\t */\n\tincrement?: bigint;\n\n\t/**\n\t * The worker ID to use, will be truncated to 5 bits (0-31)\n\t * @default 0n\n\t */\n\tworkerId?: bigint;\n\n\t/**\n\t * The process ID to use, will be truncated to 5 bits (0-31)\n\t * @default 1n\n\t */\n\tprocessId?: bigint;\n}\n\n/**\n * Object returned by Snowflake#deconstruct\n */\nexport interface DeconstructedSnowflake {\n\t/**\n\t * The id in BigInt form\n\t */\n\tid: bigint;\n\n\t/**\n\t * The timestamp stored in the snowflake\n\t */\n\ttimestamp: bigint;\n\n\t/**\n\t * The worker id stored in the snowflake\n\t */\n\tworkerId: bigint;\n\n\t/**\n\t * The process id stored in the snowflake\n\t */\n\tprocessId: bigint;\n\n\t/**\n\t * The increment stored in the snowflake\n\t */\n\tincrement: bigint;\n\n\t/**\n\t * The epoch to use in the snowflake\n\t */\n\tepoch: bigint;\n}\n", "import { Snowflake } from './Snowflake';\n\n/**\n * A class for parsing snowflake ids using Discord's snowflake epoch\n *\n * Which is 2015-01-01 at 00:00:00.000 UTC+0, {@linkplain https://discord.com/developers/docs/reference#snowflakes}\n */\nexport const DiscordSnowflake = new Snowflake(1420070400000n);\n", "import { Snowflake } from './Snowflake';\n\n/**\n * A class for parsing snowflake ids using Twitter's snowflake epoch\n *\n * Which is 2010-11-04 at 01:42:54.657 UTC+0, found in the archived snowflake repository {@linkplain https://github.com/twitter-archive/snowflake/blob/b3f6a3c6ca8e1b6847baa6ff42bf72201e2c2231/src/main/scala/com/twitter/service/snowflake/IdWorker.scala#L25}\n */\nexport const TwitterSnowflake = new Snowflake(1288834974657n);\n"]}