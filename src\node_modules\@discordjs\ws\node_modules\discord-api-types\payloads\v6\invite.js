"use strict";
/**
 * Types extracted from https://discord.com/developers/docs/resources/invite
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InviteTargetUserType = void 0;
/**
 * https://discord.com/developers/docs/resources/invite#invite-object-target-user-types
 *
 * @deprecated API and Gateway v6 are deprecated and the types will not receive further updates, please update to v8.
 */
var InviteTargetUserType;
(function (InviteTargetUserType) {
    InviteTargetUserType[InviteTargetUserType["STREAM"] = 1] = "STREAM";
})(InviteTargetUserType || (exports.InviteTargetUserType = InviteTargetUserType = {}));
//# sourceMappingURL=invite.js.map