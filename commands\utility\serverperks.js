const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('serverperks')
    .setDescription('Displays server information and rules'),
  async execute(interaction) {
    const embedColor = '#2B2D31';

    const embeds = [
      new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('SFRR | Server Perk')
        .setDescription(
          'In this channel, you will be able to see what you have once you boost the server or buy it with Robux. Once you have boosted this server or bought it with Robux, make sure to open a ticket in the support channel to get the role and perks.'
          )
        .setFooter({
          text: 'Southwest Florida Roleplay Realm'}),
    ];

    const menu = new ActionRowBuilder().addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('serverperk')
        .setPlaceholder('Click here for more information')
        .addOptions([
          {
            label: 'Boost Perks',
            description: 'View perks available for server boosters.',
            value: 'boost-perks', // Match this value in the handler
          },
          {
            label: 'Robux Perks',
            description: 'View perks available for Robux donations.',
            value: 'robux-perks', // Match this value in the handler
          },
        ])
    );

    await interaction.reply({ embeds, components: [menu], ephemeral: false });
  },
};
