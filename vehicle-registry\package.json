{"name": "vehicle-registry", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "mongoose": "^8.13.3", "passport": "^0.7.0", "passport-discord": "^0.1.4"}, "devDependencies": {"nodemon": "^3.1.10"}}