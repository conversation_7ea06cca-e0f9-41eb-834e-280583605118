import type { Snowflake } from '@discordjs/ws/node_modules/discord-api-types/globals';
import type { ChannelType } from '../../../channel';
import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
export interface APIApplicationCommandChannelOption extends APIApplicationCommandOptionBase<ApplicationCommandOptionType.Channel> {
    channel_types?: Exclude<ChannelType, ChannelType.DM | ChannelType.GroupDM>[];
}
export type APIApplicationCommandInteractionDataChannelOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Channel, Snowflake>;
//# sourceMappingURL=channel.d.ts.map