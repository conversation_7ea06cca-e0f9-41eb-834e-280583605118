const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
require('dotenv').config();

const SPAM_LOG_CHANNEL_ID = process.env.SPAM_LOG_CHANNEL_ID;
const ALLOWED_USER_ID = '1237979199747129396';
const SPAM_THRESHOLD = 5;
const SPAM_TIME_WINDOW = 10000; // 10 seconds
const WARNING_DELETE_TIMEOUT = 5000; // 5 seconds
const FORBIDDEN_PATTERNS = ['discord.gg/'];

// Validate environment variables
if (!SPAM_LOG_CHANNEL_ID) {
    console.error('SPAM_LOG_CHANNEL_ID is not set in .env file');
}

module.exports = (client) => {
    client.on('messageCreate', async (message) => {
        if (message.author.bot) return;

        // Link detection
        if (message.author.id !== ALLOWED_USER_ID) {
            const containsForbiddenLink = FORBIDDEN_PATTERNS.some(pattern => 
                message.content.toLowerCase().includes(pattern.toLowerCase())
            );

            if (containsForbiddenLink) {
                try {
                    await message.delete();
                    const warningMsg = await message.channel.send(
                        `${message.author} do not send links.`
                    );
                    setTimeout(() => warningMsg.delete().catch(() => {}), WARNING_DELETE_TIMEOUT);
                    return;
                } catch (error) {
                    console.error('Failed to handle forbidden link:', error);
                    return;
                }
            }
        }

        // Spam detection
        try {
            const now = Date.now();
            const timeWindow = now - SPAM_TIME_WINDOW;

            const recentMessages = message.channel.messages.cache.filter(msg => 
                msg.author.id === message.author.id && 
                msg.createdTimestamp > timeWindow
            );

            if (recentMessages.size > SPAM_THRESHOLD) {
                await handleSpam(message, recentMessages, client);
            }
        } catch (error) {
            console.error('Error in spam detection:', error);
        }
    });
};

async function handleSpam(message, spamMessages, client) {
    try {
        // Delete spam messages
        for (const [, msg] of spamMessages) {
            if (msg.id !== message.id) {
                await msg.delete().catch(() => {});
            }
        }

        // Send and auto-delete warning message
        const warningMessage = await message.channel.send(
            `${message.author} stop spamming within this channel.`
        );
        setTimeout(() => warningMessage.delete().catch(() => {}), WARNING_DELETE_TIMEOUT);

        // Log spam incident
        await logSpamIncident(message, client);

    } catch (error) {
        console.error('Failed to handle spam:', error);
    }
}

async function logSpamIncident(message, client) {
    if (!SPAM_LOG_CHANNEL_ID) {
        console.error('Cannot log spam incident: SPAM_LOG_CHANNEL_ID is not configured');
        return;
    }

    try {
        const logChannel = await client.channels.fetch(SPAM_LOG_CHANNEL_ID);
        if (!logChannel) {
            console.error(`Log channel with ID ${SPAM_LOG_CHANNEL_ID} not found`);
            return;
        }

        const embed = new EmbedBuilder()
            .setTitle('Spam Detection')
            .setDescription(
                `User ${message.author.tag} (${message.author.id}) was detected spamming in ${message.channel.name}.`
            )
            .setColor('#ff0000')
            .setTimestamp()
            .addFields([
                { name: 'Channel', value: `<#${message.channel.id}>`, inline: true },
                { name: 'User', value: `<@${message.author.id}>`, inline: true }
            ]);

        await logChannel.send({ embeds: [embed] });

    } catch (error) {
        console.error('Failed to log spam incident:', error);
        console.error('SPAM_LOG_CHANNEL_ID:', SPAM_LOG_CHANNEL_ID);
    }
}
