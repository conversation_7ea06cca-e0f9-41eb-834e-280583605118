const { Events, EmbedBuilder } = require('discord.js');

// Pre-define embeds for faster access
const embedColor = '#2B2D31';
const embeds = {
    'session-commands': new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Session Commands')
        .setDescription(
            '**Commands:**\n' +
            '> **/startup (reactions)**: Notify others you are hosting a session.\n' +
            '> **/setting up**: Start the session after enough reactions.\n' +
            '> **/co**: Co-host a session.\n' +
            '> **/early-access (link)**: Allow early access for certain roles.\n' +
            '> **/release**: Open the session for civilians.\n' +
            '> **/reinvites (link)**: Send reinvites post-release.\n' +
            '> **/over**: End your session officially.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Realm' }),

    'staff-quota': new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Staff Quota Requirements')
        .setDescription(
            '**Quota Requirements:**\n' +
            '- **Staff in training**: Pass training and co-host 2 sessions.\n' +
            '- **Server Staff**: 3 sessions per week.\n' +
            '- **Senior Staff**: 2 sessions per week.\n' +
            '- **Staff Supervisor**: 1 session per week, trains SIT.\n' +
            '- **Management and higher**: Exempt from quotas, ensure smooth operations.'
        )
        .setFooter({ text: 'Southwest Florida Roleplay Realm' })
};

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        if (!interaction.isStringSelectMenu() || interaction.customId !== 'embed3-menu') return;

        try {
            const embed = embeds[interaction.values[0]];
            
            await interaction.reply({
                embeds: embed ? [embed] : undefined,
                content: embed ? undefined : 'Invalid selection. Please try again.',
                ephemeral: true
            });
        } catch (error) {
            console.error('Error handling interaction:', error);
            await interaction.reply({
                content: 'There was an error while processing your selection. Please try again.',
                ephemeral: true
            }).catch(console.error);
        }
    },
};
