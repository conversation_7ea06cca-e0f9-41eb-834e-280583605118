{"version": 3, "sources": ["../src/collection.ts", "../src/index.ts"], "sourcesContent": ["/* eslint-disable no-param-reassign */\n/**\n * @internal\n */\nexport interface CollectionConstructor {\n\tnew (): Collection<unknown, unknown>;\n\tnew <Key, Value>(entries?: readonly (readonly [Key, Value])[] | null): Collection<Key, Value>;\n\tnew <Key, Value>(iterable: Iterable<readonly [Key, Value]>): Collection<Key, Value>;\n\treadonly prototype: Collection<unknown, unknown>;\n\treadonly [Symbol.species]: CollectionConstructor;\n}\n\n/**\n * Represents an immutable version of a collection\n */\nexport type ReadonlyCollection<Key, Value> = Omit<\n\tCollection<Key, Value>,\n\t'clear' | 'delete' | 'ensure' | 'forEach' | 'get' | 'reverse' | 'set' | 'sort' | 'sweep'\n> &\n\tReadonlyMap<Key, Value>;\n\n/**\n * Separate interface for the constructor so that emitted js does not have a constructor that overwrites itself\n *\n * @internal\n */\nexport interface Collection<Key, Value> extends Map<Key, Value> {\n\tconstructor: CollectionConstructor;\n}\n\n/**\n * A Map with additional utility methods. This is used throughout discord.js rather than Arrays for anything that has\n * an ID, for significantly improved performance and ease-of-use.\n *\n * @typeParam Key - The key type this collection holds\n * @typeParam Value - The value type this collection holds\n */\nexport class Collection<Key, Value> extends Map<Key, Value> {\n\t/**\n\t * Obtains the value of the given key if it exists, otherwise sets and returns the value provided by the default value generator.\n\t *\n\t * @param key - The key to get if it exists, or set otherwise\n\t * @param defaultValueGenerator - A function that generates the default value\n\t * @example\n\t * ```ts\n\t * collection.ensure(guildId, () => defaultGuildConfig);\n\t * ```\n\t */\n\tpublic ensure(key: Key, defaultValueGenerator: (key: Key, collection: this) => Value): Value {\n\t\tif (this.has(key)) return this.get(key)!;\n\t\tif (typeof defaultValueGenerator !== 'function') throw new TypeError(`${defaultValueGenerator} is not a function`);\n\t\tconst defaultValue = defaultValueGenerator(key, this);\n\t\tthis.set(key, defaultValue);\n\t\treturn defaultValue;\n\t}\n\n\t/**\n\t * Checks if all of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t * @returns `true` if all of the elements exist, `false` if at least one does not exist.\n\t */\n\tpublic hasAll(...keys: Key[]) {\n\t\treturn keys.every((key) => super.has(key));\n\t}\n\n\t/**\n\t * Checks if any of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t * @returns `true` if any of the elements exist, `false` if none exist.\n\t */\n\tpublic hasAny(...keys: Key[]) {\n\t\treturn keys.some((key) => super.has(key));\n\t}\n\n\t/**\n\t * Obtains the first value(s) in this collection.\n\t *\n\t * @param amount - Amount of values to obtain from the beginning\n\t * @returns A single value if no amount is provided or an array of values, starting from the end if amount is negative\n\t */\n\tpublic first(): Value | undefined;\n\tpublic first(amount: number): Value[];\n\tpublic first(amount?: number): Value | Value[] | undefined {\n\t\tif (amount === undefined) return this.values().next().value;\n\t\tif (amount < 0) return this.last(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.values();\n\t\treturn Array.from({ length: amount }, (): Value => iter.next().value!);\n\t}\n\n\t/**\n\t * Obtains the first key(s) in this collection.\n\t *\n\t * @param amount - Amount of keys to obtain from the beginning\n\t * @returns A single key if no amount is provided or an array of keys, starting from the end if\n\t * amount is negative\n\t */\n\tpublic firstKey(): Key | undefined;\n\tpublic firstKey(amount: number): Key[];\n\tpublic firstKey(amount?: number): Key | Key[] | undefined {\n\t\tif (amount === undefined) return this.keys().next().value;\n\t\tif (amount < 0) return this.lastKey(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.keys();\n\t\treturn Array.from({ length: amount }, (): Key => iter.next().value!);\n\t}\n\n\t/**\n\t * Obtains the last value(s) in this collection.\n\t *\n\t * @param amount - Amount of values to obtain from the end\n\t * @returns A single value if no amount is provided or an array of values, starting from the start if\n\t * amount is negative\n\t */\n\tpublic last(): Value | undefined;\n\tpublic last(amount: number): Value[];\n\tpublic last(amount?: number): Value | Value[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (amount === undefined) return arr[arr.length - 1];\n\t\tif (amount < 0) return this.first(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Obtains the last key(s) in this collection.\n\t *\n\t * @param amount - Amount of keys to obtain from the end\n\t * @returns A single key if no amount is provided or an array of keys, starting from the start if\n\t * amount is negative\n\t */\n\tpublic lastKey(): Key | undefined;\n\tpublic lastKey(amount: number): Key[];\n\tpublic lastKey(amount?: number): Key | Key[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (amount === undefined) return arr[arr.length - 1];\n\t\tif (amount < 0) return this.firstKey(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at | Array.at()}.\n\t * Returns the item at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index - The index of the element to obtain\n\t */\n\tpublic at(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.values()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at | Array.at()}.\n\t * Returns the key at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index - The index of the key to obtain\n\t */\n\tpublic keyAt(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.keys()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Obtains unique random value(s) from this collection.\n\t *\n\t * @param amount - Amount of values to obtain randomly\n\t * @returns A single value if no amount is provided or an array of values\n\t */\n\tpublic random(): Value | undefined;\n\tpublic random(amount: number): Value[];\n\tpublic random(amount?: number): Value | Value[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (amount === undefined) return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): Value => arr.splice(Math.floor(Math.random() * arr.length), 1)[0]!,\n\t\t);\n\t}\n\n\t/**\n\t * Obtains unique random key(s) from this collection.\n\t *\n\t * @param amount - Amount of keys to obtain randomly\n\t * @returns A single key if no amount is provided or an array\n\t */\n\tpublic randomKey(): Key | undefined;\n\tpublic randomKey(amount: number): Key[];\n\tpublic randomKey(amount?: number): Key | Key[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (amount === undefined) return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): Key => arr.splice(Math.floor(Math.random() * arr.length), 1)[0]!,\n\t\t);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reverse | Array.reverse()}\n\t * but returns a Collection instead of an Array.\n\t */\n\tpublic reverse() {\n\t\tconst entries = [...this.entries()].reverse();\n\t\tthis.clear();\n\t\tfor (const [key, value] of entries) this.set(key, value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Searches for a single item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find | Array.find()}.\n\t * All collections used in Discord.js are mapped using their `id` property, and if you want to find by id you\n\t * should use the `get` method. See\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/get | MDN} for details.\n\t *\n\t * @param fn - The function to test with (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.find(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic find<NewValue extends Value>(\n\t\tfn: (value: Value, key: Key, collection: this) => value is NewValue,\n\t): NewValue | undefined;\n\tpublic find(fn: (value: Value, key: Key, collection: this) => unknown): Value | undefined;\n\tpublic find<This, NewValue extends Value>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => value is NewValue,\n\t\tthisArg: This,\n\t): NewValue | undefined;\n\tpublic find<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): Value | undefined;\n\tpublic find(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): Value | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return val;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Searches for the key of a single item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findIndex | Array.findIndex()},\n\t * but returns the key rather than the positional index.\n\t *\n\t * @param fn - The function to test with (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.findKey(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic findKey<NewKey extends Key>(\n\t\tfn: (value: Value, key: Key, collection: this) => key is NewKey,\n\t): NewKey | undefined;\n\tpublic findKey(fn: (value: Value, key: Key, collection: this) => unknown): Key | undefined;\n\tpublic findKey<This, NewKey extends Key>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => key is NewKey,\n\t\tthisArg: This,\n\t): NewKey | undefined;\n\tpublic findKey<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): Key | undefined;\n\tpublic findKey(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): Key | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return key;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Searches for a last item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findLast | Array.findLast()}.\n\t *\n\t * @param fn - The function to test with (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t */\n\tpublic findLast<NewValue extends Value>(\n\t\tfn: (value: Value, key: Key, collection: this) => value is NewValue,\n\t): NewValue | undefined;\n\tpublic findLast(fn: (value: Value, key: Key, collection: this) => unknown): Value | undefined;\n\tpublic findLast<This, NewValue extends Value>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => value is NewValue,\n\t\tthisArg: This,\n\t): NewValue | undefined;\n\tpublic findLast<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): Value | undefined;\n\tpublic findLast(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): Value | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst entries = [...this.entries()];\n\t\tfor (let index = entries.length - 1; index >= 0; index--) {\n\t\t\tconst val = entries[index]![1];\n\t\t\tconst key = entries[index]![0];\n\t\t\tif (fn(val, key, this)) return val;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Searches for the key of a last item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findLastIndex | Array.findLastIndex()},\n\t * but returns the key rather than the positional index.\n\t *\n\t * @param fn - The function to test with (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t */\n\tpublic findLastKey<NewKey extends Key>(\n\t\tfn: (value: Value, key: Key, collection: this) => key is NewKey,\n\t): NewKey | undefined;\n\tpublic findLastKey(fn: (value: Value, key: Key, collection: this) => unknown): Key | undefined;\n\tpublic findLastKey<This, NewKey extends Key>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => key is NewKey,\n\t\tthisArg: This,\n\t): NewKey | undefined;\n\tpublic findLastKey<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): Key | undefined;\n\tpublic findLastKey(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): Key | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst entries = [...this.entries()];\n\t\tfor (let index = entries.length - 1; index >= 0; index--) {\n\t\t\tconst key = entries[index]![0];\n\t\t\tconst val = entries[index]![1];\n\t\t\tif (fn(val, key, this)) return key;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Removes items that satisfy the provided filter function.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @returns The number of removed entries\n\t */\n\tpublic sweep(fn: (value: Value, key: Key, collection: this) => unknown): number;\n\tpublic sweep<This>(fn: (this: This, value: Value, key: Key, collection: this) => unknown, thisArg: This): number;\n\tpublic sweep(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): number {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst previousSize = this.size;\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) this.delete(key);\n\t\t}\n\n\t\treturn previousSize - this.size;\n\t}\n\n\t/**\n\t * Identical to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter | Array.filter()},\n\t * but returns a Collection instead of an Array.\n\t *\n\t * @param fn - The function to test with (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.filter(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic filter<NewKey extends Key>(\n\t\tfn: (value: Value, key: Key, collection: this) => key is NewKey,\n\t): Collection<NewKey, Value>;\n\tpublic filter<NewValue extends Value>(\n\t\tfn: (value: Value, key: Key, collection: this) => value is NewValue,\n\t): Collection<Key, NewValue>;\n\tpublic filter(fn: (value: Value, key: Key, collection: this) => unknown): Collection<Key, Value>;\n\tpublic filter<This, NewKey extends Key>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => key is NewKey,\n\t\tthisArg: This,\n\t): Collection<NewKey, Value>;\n\tpublic filter<This, NewValue extends Value>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => value is NewValue,\n\t\tthisArg: This,\n\t): Collection<Key, NewValue>;\n\tpublic filter<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): Collection<Key, Value>;\n\tpublic filter(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): Collection<Key, Value> {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst results = new this.constructor[Symbol.species]<Key, Value>();\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) results.set(key, val);\n\t\t}\n\n\t\treturn results;\n\t}\n\n\t/**\n\t * Partitions the collection into two collections where the first collection\n\t * contains the items that passed and the second contains the items that failed.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * const [big, small] = collection.partition(guild => guild.memberCount > 250);\n\t * ```\n\t */\n\tpublic partition<NewKey extends Key>(\n\t\tfn: (value: Value, key: Key, collection: this) => key is NewKey,\n\t): [Collection<NewKey, Value>, Collection<Exclude<Key, NewKey>, Value>];\n\tpublic partition<NewValue extends Value>(\n\t\tfn: (value: Value, key: Key, collection: this) => value is NewValue,\n\t): [Collection<Key, NewValue>, Collection<Key, Exclude<Value, NewValue>>];\n\tpublic partition(\n\t\tfn: (value: Value, key: Key, collection: this) => unknown,\n\t): [Collection<Key, Value>, Collection<Key, Value>];\n\tpublic partition<This, NewKey extends Key>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => key is NewKey,\n\t\tthisArg: This,\n\t): [Collection<NewKey, Value>, Collection<Exclude<Key, NewKey>, Value>];\n\tpublic partition<This, NewValue extends Value>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => value is NewValue,\n\t\tthisArg: This,\n\t): [Collection<Key, NewValue>, Collection<Key, Exclude<Value, NewValue>>];\n\tpublic partition<This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg: This,\n\t): [Collection<Key, Value>, Collection<Key, Value>];\n\tpublic partition(\n\t\tfn: (value: Value, key: Key, collection: this) => unknown,\n\t\tthisArg?: unknown,\n\t): [Collection<Key, Value>, Collection<Key, Value>] {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst results: [Collection<Key, Value>, Collection<Key, Value>] = [\n\t\t\tnew this.constructor[Symbol.species]<Key, Value>(),\n\t\t\tnew this.constructor[Symbol.species]<Key, Value>(),\n\t\t];\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) {\n\t\t\t\tresults[0].set(key, val);\n\t\t\t} else {\n\t\t\t\tresults[1].set(key, val);\n\t\t\t}\n\t\t}\n\n\t\treturn results;\n\t}\n\n\t/**\n\t * Maps each item into a Collection, then joins the results into a single Collection. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flatMap | Array.flatMap()}.\n\t *\n\t * @param fn - Function that produces a new Collection\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.flatMap(guild => guild.members.cache);\n\t * ```\n\t */\n\tpublic flatMap<NewValue>(\n\t\tfn: (value: Value, key: Key, collection: this) => Collection<Key, NewValue>,\n\t): Collection<Key, NewValue>;\n\tpublic flatMap<NewValue, This>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => Collection<Key, NewValue>,\n\t\tthisArg: This,\n\t): Collection<Key, NewValue>;\n\tpublic flatMap<NewValue>(\n\t\tfn: (value: Value, key: Key, collection: this) => Collection<Key, NewValue>,\n\t\tthisArg?: unknown,\n\t): Collection<Key, NewValue> {\n\t\t// eslint-disable-next-line unicorn/no-array-method-this-argument\n\t\tconst collections = this.map(fn, thisArg);\n\t\treturn new this.constructor[Symbol.species]<Key, NewValue>().concat(...collections);\n\t}\n\n\t/**\n\t * Maps each item to another value into an array. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map | Array.map()}.\n\t *\n\t * @param fn - Function that produces an element of the new array, taking three arguments\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.map(user => user.tag);\n\t * ```\n\t */\n\tpublic map<NewValue>(fn: (value: Value, key: Key, collection: this) => NewValue): NewValue[];\n\tpublic map<This, NewValue>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => NewValue,\n\t\tthisArg: This,\n\t): NewValue[];\n\tpublic map<NewValue>(fn: (value: Value, key: Key, collection: this) => NewValue, thisArg?: unknown): NewValue[] {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst iter = this.entries();\n\t\treturn Array.from({ length: this.size }, (): NewValue => {\n\t\t\tconst [key, value] = iter.next().value!;\n\t\t\treturn fn(value, key, this);\n\t\t});\n\t}\n\n\t/**\n\t * Maps each item to another value into a collection. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map | Array.map()}.\n\t *\n\t * @param fn - Function that produces an element of the new collection, taking three arguments\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.mapValues(user => user.tag);\n\t * ```\n\t */\n\tpublic mapValues<NewValue>(fn: (value: Value, key: Key, collection: this) => NewValue): Collection<Key, NewValue>;\n\tpublic mapValues<This, NewValue>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => NewValue,\n\t\tthisArg: This,\n\t): Collection<Key, NewValue>;\n\tpublic mapValues<NewValue>(\n\t\tfn: (value: Value, key: Key, collection: this) => NewValue,\n\t\tthisArg?: unknown,\n\t): Collection<Key, NewValue> {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst coll = new this.constructor[Symbol.species]<Key, NewValue>();\n\t\tfor (const [key, val] of this) coll.set(key, fn(val, key, this));\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Checks if there exists an item that passes a test. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/some | Array.some()}.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.some(user => user.discriminator === '0000');\n\t * ```\n\t */\n\tpublic some(fn: (value: Value, key: Key, collection: this) => unknown): boolean;\n\tpublic some<This>(fn: (this: This, value: Value, key: Key, collection: this) => unknown, thisArg: This): boolean;\n\tpublic some(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): boolean {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t * Checks if all items passes a test. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/every | Array.every()}.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection.every(user => !user.bot);\n\t * ```\n\t */\n\tpublic every<NewKey extends Key>(\n\t\tfn: (value: Value, key: Key, collection: this) => key is NewKey,\n\t): this is Collection<NewKey, Value>;\n\tpublic every<NewValue extends Value>(\n\t\tfn: (value: Value, key: Key, collection: this) => value is NewValue,\n\t): this is Collection<Key, NewValue>;\n\tpublic every(fn: (value: Value, key: Key, collection: this) => unknown): boolean;\n\tpublic every<This, NewKey extends Key>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => key is NewKey,\n\t\tthisArg: This,\n\t): this is Collection<NewKey, Value>;\n\tpublic every<This, NewValue extends Value>(\n\t\tfn: (this: This, value: Value, key: Key, collection: this) => value is NewValue,\n\t\tthisArg: This,\n\t): this is Collection<Key, NewValue>;\n\tpublic every<This>(fn: (this: This, value: Value, key: Key, collection: this) => unknown, thisArg: This): boolean;\n\tpublic every(fn: (value: Value, key: Key, collection: this) => unknown, thisArg?: unknown): boolean {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (!fn(val, key, this)) return false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Applies a function to produce a single value. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce | Array.reduce()}.\n\t *\n\t * @param fn - Function used to reduce, taking four arguments; `accumulator`, `currentValue`, `currentKey`,\n\t * and `collection`\n\t * @param initialValue - Starting value for the accumulator\n\t * @example\n\t * ```ts\n\t * collection.reduce((acc, guild) => acc + guild.memberCount, 0);\n\t * ```\n\t */\n\tpublic reduce(\n\t\tfn: (accumulator: Value, value: Value, key: Key, collection: this) => Value,\n\t\tinitialValue?: Value,\n\t): Value;\n\tpublic reduce<InitialValue>(\n\t\tfn: (accumulator: InitialValue, value: Value, key: Key, collection: this) => InitialValue,\n\t\tinitialValue: InitialValue,\n\t): InitialValue;\n\tpublic reduce<InitialValue>(\n\t\tfn: (accumulator: InitialValue, value: Value, key: Key, collection: this) => InitialValue,\n\t\tinitialValue?: InitialValue,\n\t): InitialValue {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tlet accumulator!: InitialValue;\n\n\t\tconst iterator = this.entries();\n\t\tif (initialValue === undefined) {\n\t\t\tif (this.size === 0) throw new TypeError('Reduce of empty collection with no initial value');\n\t\t\taccumulator = iterator.next().value![1] as unknown as InitialValue;\n\t\t} else {\n\t\t\taccumulator = initialValue;\n\t\t}\n\n\t\tfor (const [key, value] of iterator) {\n\t\t\taccumulator = fn(accumulator, value, key, this);\n\t\t}\n\n\t\treturn accumulator;\n\t}\n\n\t/**\n\t * Applies a function to produce a single value. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduceRight | Array.reduceRight()}.\n\t *\n\t * @param fn - Function used to reduce, taking four arguments; `accumulator`, `value`, `key`, and `collection`\n\t * @param initialValue - Starting value for the accumulator\n\t */\n\tpublic reduceRight(\n\t\tfn: (accumulator: Value, value: Value, key: Key, collection: this) => Value,\n\t\tinitialValue?: Value,\n\t): Value;\n\tpublic reduceRight<InitialValue>(\n\t\tfn: (accumulator: InitialValue, value: Value, key: Key, collection: this) => InitialValue,\n\t\tinitialValue: InitialValue,\n\t): InitialValue;\n\tpublic reduceRight<InitialValue>(\n\t\tfn: (accumulator: InitialValue, value: Value, key: Key, collection: this) => InitialValue,\n\t\tinitialValue?: InitialValue,\n\t): InitialValue {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tconst entries = [...this.entries()];\n\t\tlet accumulator!: InitialValue;\n\n\t\tlet index: number;\n\t\tif (initialValue === undefined) {\n\t\t\tif (entries.length === 0) throw new TypeError('Reduce of empty collection with no initial value');\n\t\t\taccumulator = entries[entries.length - 1]![1] as unknown as InitialValue;\n\t\t\tindex = entries.length - 1;\n\t\t} else {\n\t\t\taccumulator = initialValue;\n\t\t\tindex = entries.length;\n\t\t}\n\n\t\twhile (--index >= 0) {\n\t\t\tconst key = entries[index]![0];\n\t\t\tconst val = entries[index]![1];\n\t\t\taccumulator = fn(accumulator, val, key, this);\n\t\t}\n\n\t\treturn accumulator;\n\t}\n\n\t/**\n\t * Identical to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/forEach | Map.forEach()},\n\t * but returns the collection instead of undefined.\n\t *\n\t * @param fn - Function to execute for each element\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection\n\t *  .each(user => console.log(user.username))\n\t *  .filter(user => user.bot)\n\t *  .each(user => console.log(user.username));\n\t * ```\n\t */\n\tpublic each(fn: (value: Value, key: Key, collection: this) => void): this;\n\tpublic each<This>(fn: (this: This, value: Value, key: Key, collection: this) => void, thisArg: This): this;\n\tpublic each(fn: (value: Value, key: Key, collection: this) => void, thisArg?: unknown): this {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\n\t\tfor (const [key, value] of this) {\n\t\t\tfn(value, key, this);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Runs a function on the collection and returns the collection.\n\t *\n\t * @param fn - Function to execute\n\t * @param thisArg - Value to use as `this` when executing the function\n\t * @example\n\t * ```ts\n\t * collection\n\t *  .tap(coll => console.log(coll.size))\n\t *  .filter(user => user.bot)\n\t *  .tap(coll => console.log(coll.size))\n\t * ```\n\t */\n\tpublic tap(fn: (collection: this) => void): this;\n\tpublic tap<This>(fn: (this: This, collection: this) => void, thisArg: This): this;\n\tpublic tap(fn: (collection: this) => void, thisArg?: unknown): this {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfn(this);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates an identical shallow copy of this collection.\n\t *\n\t * @example\n\t * ```ts\n\t * const newColl = someColl.clone();\n\t * ```\n\t */\n\tpublic clone(): Collection<Key, Value> {\n\t\treturn new this.constructor[Symbol.species](this);\n\t}\n\n\t/**\n\t * Combines this collection with others into a new collection. None of the source collections are modified.\n\t *\n\t * @param collections - Collections to merge\n\t * @example\n\t * ```ts\n\t * const newColl = someColl.concat(someOtherColl, anotherColl, ohBoyAColl);\n\t * ```\n\t */\n\tpublic concat(...collections: ReadonlyCollection<Key, Value>[]) {\n\t\tconst newColl = this.clone();\n\t\tfor (const coll of collections) {\n\t\t\tfor (const [key, val] of coll) newColl.set(key, val);\n\t\t}\n\n\t\treturn newColl;\n\t}\n\n\t/**\n\t * Checks if this collection shares identical items with another.\n\t * This is different to checking for equality using equal-signs, because\n\t * the collections may be different objects, but contain the same data.\n\t *\n\t * @param collection - Collection to compare with\n\t * @returns Whether the collections have identical contents\n\t */\n\tpublic equals(collection: ReadonlyCollection<Key, Value>) {\n\t\tif (!collection) return false; // runtime check\n\t\tif (this === collection) return true;\n\t\tif (this.size !== collection.size) return false;\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!collection.has(key) || value !== collection.get(key)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * The sort method sorts the items of a collection in place and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction - Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value, according to the string conversion of each element.\n\t * @example\n\t * ```ts\n\t * collection.sort((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t * ```\n\t */\n\tpublic sort(compareFunction: Comparator<Key, Value> = Collection.defaultSort) {\n\t\tconst entries = [...this.entries()];\n\t\tentries.sort((a, b): number => compareFunction(a[1], b[1], a[0], b[0]));\n\n\t\t// Perform clean-up\n\t\tsuper.clear();\n\n\t\t// Set the new entries\n\t\tfor (const [key, value] of entries) {\n\t\t\tsuper.set(key, value);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * The intersection method returns a new collection containing the items where the key is present in both collections.\n\t *\n\t * @param other - The other Collection to filter against\n\t * @example\n\t * ```ts\n\t * const col1 = new Collection([['a', 1], ['b', 2]]);\n\t * const col2 = new Collection([['a', 1], ['c', 3]]);\n\t * const intersection = col1.intersection(col2);\n\t * console.log(col1.intersection(col2));\n\t * // => Collection { 'a' => 1 }\n\t * ```\n\t */\n\tpublic intersection(other: ReadonlyCollection<Key, any>): Collection<Key, Value> {\n\t\tconst coll = new this.constructor[Symbol.species]<Key, Value>();\n\n\t\tfor (const [key, value] of this) {\n\t\t\tif (other.has(key)) coll.set(key, value);\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Returns a new collection containing the items where the key is present in either of the collections.\n\t *\n\t * @remarks\n\t *\n\t * If the collections have any items with the same key, the value from the first collection will be used.\n\t * @param other - The other Collection to filter against\n\t * @example\n\t * ```ts\n\t * const col1 = new Collection([['a', 1], ['b', 2]]);\n\t * const col2 = new Collection([['a', 1], ['b', 3], ['c', 3]]);\n\t * const union = col1.union(col2);\n\t * console.log(union);\n\t * // => Collection { 'a' => 1, 'b' => 2, 'c' => 3 }\n\t * ```\n\t */\n\tpublic union<OtherValue>(other: ReadonlyCollection<Key, OtherValue>): Collection<Key, OtherValue | Value> {\n\t\tconst coll = new this.constructor[Symbol.species]<Key, OtherValue | Value>(this);\n\n\t\tfor (const [key, value] of other) {\n\t\t\tif (!coll.has(key)) coll.set(key, value);\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Returns a new collection containing the items where the key is present in this collection but not the other.\n\t *\n\t * @param other - The other Collection to filter against\n\t * @example\n\t * ```ts\n\t * const col1 = new Collection([['a', 1], ['b', 2]]);\n\t * const col2 = new Collection([['a', 1], ['c', 3]]);\n\t * console.log(col1.difference(col2));\n\t * // => Collection { 'b' => 2 }\n\t * console.log(col2.difference(col1));\n\t * // => Collection { 'c' => 3 }\n\t * ```\n\t */\n\tpublic difference(other: ReadonlyCollection<Key, any>): Collection<Key, Value> {\n\t\tconst coll = new this.constructor[Symbol.species]<Key, Value>();\n\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!other.has(key)) coll.set(key, value);\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Returns a new collection containing only the items where the keys are present in either collection, but not both.\n\t *\n\t * @param other - The other Collection to filter against\n\t * @example\n\t * ```ts\n\t * const col1 = new Collection([['a', 1], ['b', 2]]);\n\t * const col2 = new Collection([['a', 1], ['c', 3]]);\n\t * const symmetricDifference = col1.symmetricDifference(col2);\n\t * console.log(col1.symmetricDifference(col2));\n\t * // => Collection { 'b' => 2, 'c' => 3 }\n\t * ```\n\t */\n\tpublic symmetricDifference<OtherValue>(\n\t\tother: ReadonlyCollection<Key, OtherValue>,\n\t): Collection<Key, OtherValue | Value> {\n\t\tconst coll = new this.constructor[Symbol.species]<Key, OtherValue | Value>();\n\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!other.has(key)) coll.set(key, value);\n\t\t}\n\n\t\tfor (const [key, value] of other) {\n\t\t\tif (!this.has(key)) coll.set(key, value);\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Merges two Collections together into a new Collection.\n\t *\n\t * @param other - The other Collection to merge with\n\t * @param whenInSelf - Function getting the result if the entry only exists in this Collection\n\t * @param whenInOther - Function getting the result if the entry only exists in the other Collection\n\t * @param whenInBoth - Function getting the result if the entry exists in both Collections\n\t * @example\n\t * ```ts\n\t * // Sums up the entries in two collections.\n\t * coll.merge(\n\t *  other,\n\t *  x => ({ keep: true, value: x }),\n\t *  y => ({ keep: true, value: y }),\n\t *  (x, y) => ({ keep: true, value: x + y }),\n\t * );\n\t * ```\n\t * @example\n\t * ```ts\n\t * // Intersects two collections in a left-biased manner.\n\t * coll.merge(\n\t *  other,\n\t *  x => ({ keep: false }),\n\t *  y => ({ keep: false }),\n\t *  (x, _) => ({ keep: true, value: x }),\n\t * );\n\t * ```\n\t */\n\tpublic merge<OtherValue, ResultValue>(\n\t\tother: ReadonlyCollection<Key, OtherValue>,\n\t\twhenInSelf: (value: Value, key: Key) => Keep<ResultValue>,\n\t\twhenInOther: (valueOther: OtherValue, key: Key) => Keep<ResultValue>,\n\t\twhenInBoth: (value: Value, valueOther: OtherValue, key: Key) => Keep<ResultValue>,\n\t): Collection<Key, ResultValue> {\n\t\tconst coll = new this.constructor[Symbol.species]<Key, ResultValue>();\n\t\tconst keys = new Set([...this.keys(), ...other.keys()]);\n\n\t\tfor (const key of keys) {\n\t\t\tconst hasInSelf = this.has(key);\n\t\t\tconst hasInOther = other.has(key);\n\n\t\t\tif (hasInSelf && hasInOther) {\n\t\t\t\tconst result = whenInBoth(this.get(key)!, other.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t} else if (hasInSelf) {\n\t\t\t\tconst result = whenInSelf(this.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t} else if (hasInOther) {\n\t\t\t\tconst result = whenInOther(other.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/toReversed | Array.toReversed()}\n\t * but returns a Collection instead of an Array.\n\t */\n\tpublic toReversed() {\n\t\treturn new this.constructor[Symbol.species](this).reverse();\n\t}\n\n\t/**\n\t * The sorted method sorts the items of a collection and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction - Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value,\n\t * according to the string conversion of each element.\n\t * @example\n\t * ```ts\n\t * collection.sorted((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t * ```\n\t */\n\tpublic toSorted(compareFunction: Comparator<Key, Value> = Collection.defaultSort) {\n\t\treturn new this.constructor[Symbol.species](this).sort((av, bv, ak, bk) => compareFunction(av, bv, ak, bk));\n\t}\n\n\tpublic toJSON() {\n\t\t// toJSON is called recursively by JSON.stringify.\n\t\treturn [...this.entries()];\n\t}\n\n\tprivate static defaultSort<Value>(firstValue: Value, secondValue: Value): number {\n\t\treturn Number(firstValue > secondValue) || Number(firstValue === secondValue) - 1;\n\t}\n\n\t/**\n\t * Creates a Collection from a list of entries.\n\t *\n\t * @param entries - The list of entries\n\t * @param combine - Function to combine an existing entry with a new one\n\t * @example\n\t * ```ts\n\t * Collection.combineEntries([[\"a\", 1], [\"b\", 2], [\"a\", 2]], (x, y) => x + y);\n\t * // returns Collection { \"a\" => 3, \"b\" => 2 }\n\t * ```\n\t */\n\tpublic static combineEntries<Key, Value>(\n\t\tentries: Iterable<[Key, Value]>,\n\t\tcombine: (firstValue: Value, secondValue: Value, key: Key) => Value,\n\t): Collection<Key, Value> {\n\t\tconst coll = new Collection<Key, Value>();\n\t\tfor (const [key, value] of entries) {\n\t\t\tif (coll.has(key)) {\n\t\t\t\tcoll.set(key, combine(coll.get(key)!, value, key));\n\t\t\t} else {\n\t\t\t\tcoll.set(key, value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n}\n\n/**\n * @internal\n */\nexport type Keep<Value> = { keep: false } | { keep: true; value: Value };\n\n/**\n * @internal\n */\nexport type Comparator<Key, Value> = (firstValue: Value, secondValue: Value, firstKey: Key, secondKey: Key) => number;\n", "export * from './collection.js';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/collection#readme | @discordjs/collection} version\n * that you are currently using.\n */\n// This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild\nexport const version = '2.1.1' as string;\n"], "mappings": ";;;;AAqCO,IAAM,aAAN,MAAM,oBAA+B,IAAgB;AAAA,EArC5D,OAqC4D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWpD,OAAO,KAAU,uBAAqE;AAC5F,QAAI,KAAK,IAAI,GAAG,EAAG,QAAO,KAAK,IAAI,GAAG;AACtC,QAAI,OAAO,0BAA0B,WAAY,OAAM,IAAI,UAAU,GAAG,qBAAqB,oBAAoB;AACjH,UAAM,eAAe,sBAAsB,KAAK,IAAI;AACpD,SAAK,IAAI,KAAK,YAAY;AAC1B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,MAAa;AAC7B,WAAO,KAAK,MAAM,CAAC,QAAQ,MAAM,IAAI,GAAG,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,MAAa;AAC7B,WAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,GAAG,CAAC;AAAA,EACzC;AAAA,EAUO,MAAM,QAA8C;AAC1D,QAAI,WAAW,OAAW,QAAO,KAAK,OAAO,EAAE,KAAK,EAAE;AACtD,QAAI,SAAS,EAAG,QAAO,KAAK,KAAK,SAAS,EAAE;AAC5C,aAAS,KAAK,IAAI,KAAK,MAAM,MAAM;AACnC,UAAM,OAAO,KAAK,OAAO;AACzB,WAAO,MAAM,KAAK,EAAE,QAAQ,OAAO,GAAG,MAAa,KAAK,KAAK,EAAE,KAAM;AAAA,EACtE;AAAA,EAWO,SAAS,QAA0C;AACzD,QAAI,WAAW,OAAW,QAAO,KAAK,KAAK,EAAE,KAAK,EAAE;AACpD,QAAI,SAAS,EAAG,QAAO,KAAK,QAAQ,SAAS,EAAE;AAC/C,aAAS,KAAK,IAAI,KAAK,MAAM,MAAM;AACnC,UAAM,OAAO,KAAK,KAAK;AACvB,WAAO,MAAM,KAAK,EAAE,QAAQ,OAAO,GAAG,MAAW,KAAK,KAAK,EAAE,KAAM;AAAA,EACpE;AAAA,EAWO,KAAK,QAA8C;AACzD,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,QAAI,WAAW,OAAW,QAAO,IAAI,IAAI,SAAS,CAAC;AACnD,QAAI,SAAS,EAAG,QAAO,KAAK,MAAM,SAAS,EAAE;AAC7C,QAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,WAAO,IAAI,MAAM,CAAC,MAAM;AAAA,EACzB;AAAA,EAWO,QAAQ,QAA0C;AACxD,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,QAAI,WAAW,OAAW,QAAO,IAAI,IAAI,SAAS,CAAC;AACnD,QAAI,SAAS,EAAG,QAAO,KAAK,SAAS,SAAS,EAAE;AAChD,QAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,WAAO,IAAI,MAAM,CAAC,MAAM;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,GAAG,OAAe;AACxB,YAAQ,KAAK,MAAM,KAAK;AACxB,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,WAAO,IAAI,GAAG,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,MAAM,OAAe;AAC3B,YAAQ,KAAK,MAAM,KAAK;AACxB,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,WAAO,IAAI,GAAG,KAAK;AAAA,EACpB;AAAA,EAUO,OAAO,QAA8C;AAC3D,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,QAAI,WAAW,OAAW,QAAO,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAC3E,QAAI,CAAC,IAAI,UAAU,CAAC,OAAQ,QAAO,CAAC;AACpC,WAAO,MAAM;AAAA,MACZ,EAAE,QAAQ,KAAK,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,MACvC,MAAa,IAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;AAAA,IACrE;AAAA,EACD;AAAA,EAUO,UAAU,QAA0C;AAC1D,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,QAAI,WAAW,OAAW,QAAO,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAC3E,QAAI,CAAC,IAAI,UAAU,CAAC,OAAQ,QAAO,CAAC;AACpC,WAAO,MAAM;AAAA,MACZ,EAAE,QAAQ,KAAK,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,MACvC,MAAW,IAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;AAAA,IACnE;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,UAAU;AAChB,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAC5C,SAAK,MAAM;AACX,eAAW,CAAC,KAAK,KAAK,KAAK,QAAS,MAAK,IAAI,KAAK,KAAK;AACvD,WAAO;AAAA,EACR;AAAA,EA4BO,KAAK,IAA2D,SAAsC;AAC5G,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EA0BO,QAAQ,IAA2D,SAAoC;AAC7G,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAqBO,SAAS,IAA2D,SAAsC;AAChH,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,aAAS,QAAQ,QAAQ,SAAS,GAAG,SAAS,GAAG,SAAS;AACzD,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAsBO,YAAY,IAA2D,SAAoC;AACjH,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,aAAS,QAAQ,QAAQ,SAAS,GAAG,SAAS,GAAG,SAAS;AACzD,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAWO,MAAM,IAA2D,SAA2B;AAClG,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,eAAe,KAAK;AAC1B,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,MAAK,OAAO,GAAG;AAAA,IACxC;AAEA,WAAO,eAAe,KAAK;AAAA,EAC5B;AAAA,EAiCO,OAAO,IAA2D,SAA2C;AACnH,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAAU,IAAI,KAAK,YAAY,OAAO,OAAO,EAAc;AACjE,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,SAAQ,IAAI,KAAK,GAAG;AAAA,IAC7C;AAEA,WAAO;AAAA,EACR;AAAA,EAkCO,UACN,IACA,SACmD;AACnD,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAA4D;AAAA,MACjE,IAAI,KAAK,YAAY,OAAO,OAAO,EAAc;AAAA,MACjD,IAAI,KAAK,YAAY,OAAO,OAAO,EAAc;AAAA,IAClD;AACA,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,GAAG;AACvB,gBAAQ,CAAC,EAAE,IAAI,KAAK,GAAG;AAAA,MACxB,OAAO;AACN,gBAAQ,CAAC,EAAE,IAAI,KAAK,GAAG;AAAA,MACxB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAoBO,QACN,IACA,SAC4B;AAE5B,UAAM,cAAc,KAAK,IAAI,IAAI,OAAO;AACxC,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAiB,EAAE,OAAO,GAAG,WAAW;AAAA,EACnF;AAAA,EAkBO,IAAc,IAA4D,SAA+B;AAC/G,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,OAAO,KAAK,QAAQ;AAC1B,WAAO,MAAM,KAAK,EAAE,QAAQ,KAAK,KAAK,GAAG,MAAgB;AACxD,YAAM,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;AACjC,aAAO,GAAG,OAAO,KAAK,IAAI;AAAA,IAC3B,CAAC;AAAA,EACF;AAAA,EAkBO,UACN,IACA,SAC4B;AAC5B,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAiB;AACjE,eAAW,CAAC,KAAK,GAAG,KAAK,KAAM,MAAK,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC;AAC/D,WAAO;AAAA,EACR;AAAA,EAeO,KAAK,IAA2D,SAA4B;AAClG,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EA6BO,MAAM,IAA2D,SAA4B;AACnG,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,CAAC,GAAG,KAAK,KAAK,IAAI,EAAG,QAAO;AAAA,IACjC;AAEA,WAAO;AAAA,EACR;AAAA,EAsBO,OACN,IACA,cACe;AACf,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI;AAEJ,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAI,iBAAiB,QAAW;AAC/B,UAAI,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,kDAAkD;AAC3F,oBAAc,SAAS,KAAK,EAAE,MAAO,CAAC;AAAA,IACvC,OAAO;AACN,oBAAc;AAAA,IACf;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,UAAU;AACpC,oBAAc,GAAG,aAAa,OAAO,KAAK,IAAI;AAAA,IAC/C;AAEA,WAAO;AAAA,EACR;AAAA,EAiBO,YACN,IACA,cACe;AACf,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,QAAI;AAEJ,QAAI;AACJ,QAAI,iBAAiB,QAAW;AAC/B,UAAI,QAAQ,WAAW,EAAG,OAAM,IAAI,UAAU,kDAAkD;AAChG,oBAAc,QAAQ,QAAQ,SAAS,CAAC,EAAG,CAAC;AAC5C,cAAQ,QAAQ,SAAS;AAAA,IAC1B,OAAO;AACN,oBAAc;AACd,cAAQ,QAAQ;AAAA,IACjB;AAEA,WAAO,EAAE,SAAS,GAAG;AACpB,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,YAAM,MAAM,QAAQ,KAAK,EAAG,CAAC;AAC7B,oBAAc,GAAG,aAAa,KAAK,KAAK,IAAI;AAAA,IAC7C;AAEA,WAAO;AAAA,EACR;AAAA,EAmBO,KAAK,IAAwD,SAAyB;AAC5F,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAE/C,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,SAAG,OAAO,KAAK,IAAI;AAAA,IACpB;AAEA,WAAO;AAAA,EACR;AAAA,EAiBO,IAAI,IAAgC,SAAyB;AACnE,QAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY,OAAW,MAAK,GAAG,KAAK,OAAO;AAC/C,OAAG,IAAI;AACP,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,QAAgC;AACtC,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAE,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,UAAU,aAA+C;AAC/D,UAAM,UAAU,KAAK,MAAM;AAC3B,eAAW,QAAQ,aAAa;AAC/B,iBAAW,CAAC,KAAK,GAAG,KAAK,KAAM,SAAQ,IAAI,KAAK,GAAG;AAAA,IACpD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,OAAO,YAA4C;AACzD,QAAI,CAAC,WAAY,QAAO;AACxB,QAAI,SAAS,WAAY,QAAO;AAChC,QAAI,KAAK,SAAS,WAAW,KAAM,QAAO;AAC1C,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,WAAW,IAAI,GAAG,KAAK,UAAU,WAAW,IAAI,GAAG,GAAG;AAC1D,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcO,KAAK,kBAA0C,YAAW,aAAa;AAC7E,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,YAAQ,KAAK,CAAC,GAAG,MAAc,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAGtE,UAAM,MAAM;AAGZ,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AACnC,YAAM,IAAI,KAAK,KAAK;AAAA,IACrB;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,aAAa,OAA6D;AAChF,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAc;AAE9D,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,MAAM,IAAI,GAAG,EAAG,MAAK,IAAI,KAAK,KAAK;AAAA,IACxC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBO,MAAkB,OAAiF;AACzG,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAA2B,IAAI;AAE/E,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AACjC,UAAI,CAAC,KAAK,IAAI,GAAG,EAAG,MAAK,IAAI,KAAK,KAAK;AAAA,IACxC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBO,WAAW,OAA6D;AAC9E,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAc;AAE9D,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,MAAM,IAAI,GAAG,EAAG,MAAK,IAAI,KAAK,KAAK;AAAA,IACzC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,oBACN,OACsC;AACtC,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAA2B;AAE3E,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,MAAM,IAAI,GAAG,EAAG,MAAK,IAAI,KAAK,KAAK;AAAA,IACzC;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AACjC,UAAI,CAAC,KAAK,IAAI,GAAG,EAAG,MAAK,IAAI,KAAK,KAAK;AAAA,IACxC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BO,MACN,OACA,YACA,aACA,YAC+B;AAC/B,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAoB;AACpE,UAAM,OAAO,oBAAI,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,GAAG,MAAM,KAAK,CAAC,CAAC;AAEtD,eAAW,OAAO,MAAM;AACvB,YAAM,YAAY,KAAK,IAAI,GAAG;AAC9B,YAAM,aAAa,MAAM,IAAI,GAAG;AAEhC,UAAI,aAAa,YAAY;AAC5B,cAAM,SAAS,WAAW,KAAK,IAAI,GAAG,GAAI,MAAM,IAAI,GAAG,GAAI,GAAG;AAC9D,YAAI,OAAO,KAAM,MAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C,WAAW,WAAW;AACrB,cAAM,SAAS,WAAW,KAAK,IAAI,GAAG,GAAI,GAAG;AAC7C,YAAI,OAAO,KAAM,MAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C,WAAW,YAAY;AACtB,cAAM,SAAS,YAAY,MAAM,IAAI,GAAG,GAAI,GAAG;AAC/C,YAAI,OAAO,KAAM,MAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,aAAa;AACnB,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAE,IAAI,EAAE,QAAQ;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,SAAS,kBAA0C,YAAW,aAAa;AACjF,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,OAAO,gBAAgB,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,EAC3G;AAAA,EAEO,SAAS;AAEf,WAAO,CAAC,GAAG,KAAK,QAAQ,CAAC;AAAA,EAC1B;AAAA,EAEA,OAAe,YAAmB,YAAmB,aAA4B;AAChF,WAAO,OAAO,aAAa,WAAW,KAAK,OAAO,eAAe,WAAW,IAAI;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAc,eACb,SACA,SACyB;AACzB,UAAM,OAAO,IAAI,YAAuB;AACxC,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AACnC,UAAI,KAAK,IAAI,GAAG,GAAG;AAClB,aAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,GAAG,GAAI,OAAO,GAAG,CAAC;AAAA,MAClD,OAAO;AACN,aAAK,IAAI,KAAK,KAAK;AAAA,MACpB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AACD;;;ACrgCO,IAAM,UAAU;", "names": []}