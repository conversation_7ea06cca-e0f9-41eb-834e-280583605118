import Dispatcher from "undici/types/dispatcher";

export declare class Redire<PERSON><PERSON><PERSON><PERSON> implements Dispatcher.DispatchHandlers {
  constructor(
    dispatch: Dispatcher,
    maxRedirections: number,
    opts: Dispatcher.DispatchOptions,
    handler: Dispatcher.DispatchHandlers,
    redirectionLimitReached: boolean
  );
}

export declare class Decorator<PERSON>andler implements Dispatcher.DispatchHandlers {
  constructor(handler: Dispatcher.DispatchHandlers);
}
