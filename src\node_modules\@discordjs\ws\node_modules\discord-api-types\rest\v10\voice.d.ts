import type { APIVoiceRegion } from '@discordjs/ws/node_modules/discord-api-types/payloads/v10';
/**
 * https://discord.com/developers/docs/resources/voice#list-voice-regions
 */
export type RESTGetAPIVoiceRegionsResult = APIVoiceRegion[];
/**
 * @deprecated This was exported with the wrong name, use `RESTGetAPIVoiceRegionsResult` instead
 */
export type GetAPIVoiceRegionsResult = RESTGetAPIVoiceRegionsResult;
//# sourceMappingURL=voice.d.ts.map