require("dotenv").config();
const { Client, Collection, GatewayIntentBits } = require("discord.js");
const fs = require("fs");
const { REST } = require("@discordjs/rest");
const { Routes } = require("discord-api-types/v9");

const token = process.env.TOKEN;
const clientId = "1242613284549558402"; // Your bot's client ID
const guildId = "1237978330079432734";  // Your Discord server (guild) ID

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
  ],
});

client.commands = new Collection();
client.commandArray = [];

// ✅ Load Events
const loadEvents = async () => {
  const eventFiles = fs.readdirSync("./events").filter(file => file.endsWith(".js"));
  for (const file of eventFiles) {
    const event = require(`./events/${file}`);
    if (event.once) {
      client.once(event.name, (...args) => event.execute(...args, client));
    } else {
      client.on(event.name, (...args) => event.execute(...args, client));
    }
  }
};

// ✅ Load Commands
const loadCommands = async () => {
  const commandFolders = fs.readdirSync("./commands");

  for (const folder of commandFolders) {
    const folderPath = `./commands/${folder}`;
    
    // Check if it's a directory or a file
    const stats = fs.statSync(folderPath);
    if (stats.isDirectory()) {
      const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith(".js"));
      for (const file of commandFiles) {
        try {
          const command = require(`${folderPath}/${file}`);
          if (!command.data || typeof command.data.toJSON !== "function") {
            throw new TypeError(`Command file ${folder}/${file} is invalid.`);
          }
          client.commands.set(command.data.name, command);
          client.commandArray.push(command.data.toJSON());
        } catch (error) {
          console.error(`Error in command file ${folder}/${file}:`, error);
        }
      }
    } else if (folder.endsWith(".js")) {
      try {
        const command = require(`./commands/${folder}`);
        if (!command.data || typeof command.data.toJSON !== "function") {
          throw new TypeError(`Command file ${folder} is invalid.`);
        }
        client.commands.set(command.data.name, command);
        client.commandArray.push(command.data.toJSON());
      } catch (error) {
        console.error(`Error in command file ${folder}:`, error);
      }
    }
  }

  // ✅ Register Commands with Discord
  const rest = new REST({ version: "9" }).setToken(token);
  try {
    await rest.put(Routes.applicationGuildCommands(clientId, guildId), {
      body: client.commandArray,
    });
    console.log("✅ Slash commands registered successfully.");
  } catch (error) {
    console.error("❌ Error registering commands:", error);
  }
};

// ✅ Initialize Bot
(async () => {
  await loadEvents();
  await loadCommands();
  await client.login(token);
  console.log("✅ Bot is online!");
})();
