{"version": 3, "file": "channel.d.ts", "sourceRoot": "", "sources": ["channel.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,SAAS,EACT,UAAU,EACV,mBAAmB,EACnB,YAAY,EACZ,OAAO,EACP,WAAW,EACX,oBAAoB,EACpB,YAAY,EACZ,aAAa,EACb,MAAM,yBAAyB,CAAC;AAIjC;;;;GAIG;AACH,MAAM,WAAW,gBAAgB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,aAAa,CAAC;IACpB,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;CACtB;AAED;;;;GAIG;AACH,oBAAY,oBAAoB;IAC/B,QAAQ,aAAa;IACrB,IAAI,UAAU;IACd,IAAI,UAAU;CACd;AAED;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACtC,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC;IAC/B,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;CACjB;AAID;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC3C,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,IAAI,CAAC,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,GAAG,SAAS,CAAC;IACnE,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACvC,qBAAqB,CAAC,EAAE,YAAY,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IAC1D,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACtC;AAED;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,UAAU,CAAC;AAEjD;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAAC;AAEnD;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG,UAAU,CAAC;AAEpD;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,EAAE,CAAC;AAE3D;;;;GAIG;AACH,MAAM,WAAW,iCAAiC;IACjD,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1B,KAAK,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC7B,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;IACtD,iBAAiB,CAAC,EAAE,mBAAmB,GAAG,SAAS,CAAC;CACpD;AAED;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAC9C;IACA;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAClC;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACb,GACD;IACA,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACpC,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1B,KAAK,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC7B,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;IACtD,iBAAiB,CAAC,EAAE,mBAAmB,GAAG,SAAS,CAAC;IACpD;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACb,CAAC;AAEL;;;;GAIG;AACH,MAAM,WAAW,kCAAkC;IAClD,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7D,KAAK,CAAC,EAAE,YAAY,GAAG,IAAI,GAAG,SAAS,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG,UAAU,CAAC;AAExD;;GAEG;AACH,MAAM,MAAM,+BAA+B,GAAG,UAAU,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,UAAU,CAAC;AAE1D;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;;GAIG;AACH,MAAM,WAAW,sCAAsC;IACtD,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,OAAO,EAAE,CAAC;AAEhE;;GAEG;AACH,MAAM,MAAM,sCAAsC,GAAG,KAAK,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,yCAAyC,GAAG,KAAK,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,6CAA6C,GAAG,KAAK,CAAC;AAElE;;;;GAIG;AACH,MAAM,WAAW,4CAA4C;IAC5D,QAAQ,EAAE,MAAM,EAAE,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,MAAM,0CAA0C,GAAG,KAAK,CAAC;AAE/D;;;;GAIG;AACH,MAAM,WAAW,oCAAoC;IACpD,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;IACtB,IAAI,EAAE,aAAa,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG,KAAK,CAAC;AAE1D;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,SAAS,EAAE,CAAC;AAEzD;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAChD,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B,cAAc,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC,gBAAgB,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;CACpD;AAED;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,UAAU,EAAE,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,KAAK,CAAC;AAE/C;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;;GAIG;AACH,MAAM,WAAW,kCAAkC;IAClD,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,MAAM,gCAAgC,GAAG,OAAO,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG,OAAO,CAAC;AAI1D;;GAEG;AACH,MAAM,MAAM,wCAAwC,GAAG,UAAU,CAAC;AAElE;;GAEG;AACH,MAAM,WAAW,mCAAmC;IACnD,kBAAkB,EAAE,MAAM,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,kBAAkB,CAAC"}