
name: Build and test
on:
  push:
    branches: [master]
jobs:
  buildAndTest:
    name: Build and test (Node.js v${{ matrix.node-version }})
    timeout-minutes: 15
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests
      id-token: write # to enable use of OIDC for npm provenance
    strategy:
      matrix:
        node-version: [18]
    steps:
      - uses: actions/checkout@v2
      - name: Install Node.js
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'
      - name: Install install dependencies
        run: npm ci
      - name: test
        run: npm test
      - name: build
        run: npm run build
      - name: release new version
        run: npm run semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      #- run: npm publish
      #  env:
      #    NODE_AUTH_TOKEN: ${{ secrets.NPM_SECRET_TOKEN }}
