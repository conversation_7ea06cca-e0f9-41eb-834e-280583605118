{"$schema": "https://json.schemastore.org/package.json", "name": "discord.js", "version": "14.17.2", "description": "A powerful library for interacting with the Discord API", "main": "./src/index.js", "types": "./typings/index.d.ts", "exports": {".": {"import": {"types": "./typings/index.d.mts", "default": "./src/index.js"}, "require": {"types": "./typings/index.d.ts", "default": "./src/index.js"}}}, "directories": {"lib": "src", "test": "test"}, "files": ["src", "typings"], "contributors": ["Crawl <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "SpaceEEC <<EMAIL>>", "<PERSON><PERSON> Rom<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "keywords": ["discord", "api", "bot", "client", "node", "discordapp"], "repository": {"type": "git", "url": "https://github.com/discordjs/discord.js.git", "directory": "packages/discord.js"}, "bugs": {"url": "https://github.com/discordjs/discord.js/issues"}, "homepage": "https://discord.js.org", "funding": "https://github.com/discordjs/discord.js?sponsor", "dependencies": {"@discordjs/builders": "^1.10.0", "@discordjs/collection": "1.5.3", "@discordjs/formatters": "^0.6.0", "@discordjs/ws": "^1.2.0", "@sapphire/snowflake": "3.5.3", "discord-api-types": "^0.37.114", "fast-deep-equal": "3.1.3", "lodash.snakecase": "4.1.1", "tslib": "^2.6.3", "undici": "6.19.8", "@discordjs/rest": "^2.4.2", "@discordjs/util": "^1.1.1"}, "devDependencies": {"@favware/cliff-jumper": "^4.1.0", "@types/node": "^16.18.105", "@typescript-eslint/eslint-plugin": "^8.2.0", "@typescript-eslint/parser": "^8.2.0", "cross-env": "^7.0.3", "dtslint": "4.2.1", "eslint": "^8.57.0", "eslint-formatter-pretty": "^5.0.0", "jest": "29.7.0", "prettier": "^3.3.3", "tsd": "^0.31.1", "tslint": "6.1.3", "turbo": "^2.0.14", "typescript": "~5.5.4", "@discordjs/api-extractor": "^7.38.1", "@discordjs/docgen": "^0.12.1", "@discordjs/scripts": "^0.1.0"}, "engines": {"node": ">=18"}, "publishConfig": {"provenance": true}, "scripts": {"test": "pnpm run docs:test && pnpm run test:typescript", "test:typescript": "tsc --noEmit && tsd", "lint": "prettier --check . && tslint typings/index.d.ts && cross-env ESLINT_USE_FLAT_CONFIG=false eslint --format=pretty src typings", "format": "prettier --write . && cross-env ESLINT_USE_FLAT_CONFIG=false eslint --fix --format=pretty src", "fmt": "pnpm run format", "docs": "docgen -i \"./src/*.js\" \"./src/**/*.js\" -c ./docs/index.json -r ../../ -o ./docs/docs.json && pnpm run docs:new", "docs:test": "docgen -i \"./src/*.js\" \"./src/**/*.js\" -c ./docs/index.json -r ../../", "docs:new": "api-extractor run --local --minify && generate-split-documentation", "changelog": "git cliff --prepend ./CHANGELOG.md -u -c ./cliff.toml -r ../../ --include-path 'packages/discord.js/*'", "release": "cliff-jumper"}}