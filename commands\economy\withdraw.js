const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getBalance, updateBalance } = require('../../utils/economy');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('withdraw')
        .setDescription('Withdraw money from your bank')
        .addStringOption(option =>
            option.setName('amount')
                .setDescription('Amount to withdraw (use "all" for everything)')
                .setRequired(true)),

    async execute(interaction) {
        const amount = interaction.options.getString('amount');
        const balance = await getBalance(interaction.user.id);

        let withdrawAmount;
        if (amount.toLowerCase() === 'all') {
            withdrawAmount = balance.bank;
        } else {
            withdrawAmount = parseInt(amount);
            if (isNaN(withdrawAmount) || withdrawAmount <= 0) {
                return interaction.reply({
                    content: 'Please provide a valid positive number or "all".',
                    ephemeral: true
                });
            }
        }

        if (withdrawAmount > balance.bank) {
            return interaction.reply({
                content: 'You don\'t have that much money in your bank!',
                ephemeral: true
            });
        }

        await updateBalance(interaction.user.id, withdrawAmount, 'wallet');
        await updateBalance(interaction.user.id, -withdrawAmount, 'bank');

        const embed = new EmbedBuilder()
            .setColor('#2B2D31')
            .setTitle('Withdrawal Successful')
            .setDescription(`Withdrew $${withdrawAmount.toLocaleString()} from your bank account.`)
            .setFooter({ text: 'Southwest Florida Roleplay Realm' });

        await interaction.reply({ embeds: [embed] });
    },
};