const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

const GIVEAWAYS_FILE = path.join(__dirname, '../../data/giveaways.json');

// Load giveaways from file
function loadGiveaways() {
    try {
        if (fs.existsSync(GIVEAWAYS_FILE)) {
            return JSON.parse(fs.readFileSync(GIVEAWAYS_FILE, 'utf8'));
        }
    } catch (error) {
        console.error('Error loading giveaways:', error);
    }
    return {};
}

// Save giveaways to file
function saveGiveaways(giveaways) {
    try {
        fs.writeFileSync(GIVEAWAYS_FILE, JSON.stringify(giveaways, null, 2));
    } catch (error) {
        console.error('Error saving giveaways:', error);
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('gend')
        .setDescription('End a giveaway early')
        .addStringOption(option =>
            option.setName('message_id')
                .setDescription('Message ID of the giveaway to end')
                .setRequired(true)),

    async execute(interaction) {
        // Check permissions
        if (!interaction.member.permissions.has('ManageMessages')) {
            return await interaction.reply({
                content: 'You need the `Manage Messages` permission to end giveaways.',
                ephemeral: true
            });
        }

        const messageId = interaction.options.getString('message_id');
        
        await interaction.deferReply({ ephemeral: true });

        try {
            const giveaways = loadGiveaways();
            
            // Find giveaway by message ID
            let giveawayId = null;
            let giveaway = null;
            
            for (const [id, data] of Object.entries(giveaways)) {
                if (data.messageId === messageId) {
                    giveawayId = id;
                    giveaway = data;
                    break;
                }
            }
            
            if (!giveaway) {
                return await interaction.editReply({
                    content: 'No giveaway found with that message ID.'
                });
            }
            
            if (giveaway.ended) {
                return await interaction.editReply({
                    content: 'This giveaway has already ended.'
                });
            }
            
            // Check if user is the host or has admin permissions
            if (giveaway.hostId !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
                return await interaction.editReply({
                    content: 'You can only end giveaways that you hosted, unless you have Administrator permissions.'
                });
            }
            
            // End the giveaway
            const channel = await interaction.guild.channels.fetch(giveaway.channelId);
            const message = await channel.messages.fetch(giveaway.messageId);
            
            // Mark as ended
            giveaway.ended = true;
            giveaways[giveawayId] = giveaway;
            saveGiveaways(giveaways);
            
            // Select winners
            const entries = giveaway.entries;
            if (entries.length === 0) {
                // No entries
                const embed = new EmbedBuilder()
                    .setTitle('🎉 GIVEAWAY ENDED 🎉')
                    .setDescription(`**Prize:** ${giveaway.prize}\n\n No valid entries! No winners selected.\n\n**Ended early by:** ${interaction.user}`)
                    .setColor('#FF0000')
                    .setFooter({ text: 'Giveaway ended early' })
                    .setTimestamp();
                    
                await message.edit({ embeds: [embed], components: [] });
                
                return await interaction.editReply({
                    content: 'Giveaway ended early. No winners were selected due to no entries.'
                });
            }
            
            // Select random winners
            const shuffled = [...entries].sort(() => 0.5 - Math.random());
            const winners = shuffled.slice(0, Math.min(giveaway.winners, entries.length));
            
            // Update embed
            const embed = new EmbedBuilder()
                .setTitle('🎉 GIVEAWAY ENDED 🎉')
                .setDescription(`**Prize:** ${giveaway.prize}\n\n🏆 **Winner${winners.length > 1 ? 's' : ''}:** ${winners.map(id => `<@${id}>`).join(', ')}\n\n**Hosted by:** <@${giveaway.hostId}>\n**Ended early by:** ${interaction.user}`)
                .setColor('#00FF00')
                .setFooter({ text: 'Giveaway ended early' })
                .setTimestamp();
                
            await message.edit({ embeds: [embed], components: [] });
            
            // Announce winners
            await channel.send(`🎉 Congratulations ${winners.map(id => `<@${id}>`).join(', ')}! You won **${giveaway.prize}**!`);
            
            await interaction.editReply({
                content: `Giveaway ended early! Winners: ${winners.map(id => `<@${id}>`).join(', ')}`
            });
            
        } catch (error) {
            console.error('Error ending giveaway:', error);
            await interaction.editReply({
                content: 'An error occurred while ending the giveaway. Please check the message ID and try again.'
            });
        }
    }
};
