# Changelog

All notable changes to this project will be documented in this file.

# [4.0.0](https://github.com/sapphiredev/shapeshift/compare/v3.9.7...v4.0.0) - (2024-05-20)

## 🚀 Features

- ***:** Add custom message options to all shapes, validators and constraints (#231) ([44a5cea](https://github.com/sapphiredev/shapeshift/commit/44a5cea211ebe409316d3d9e83afb01051a14498))
  - 💥 **BREAKING CHANGE:** Most shapes and validators that were previously getters are now functions to allow for custom options. The following list should show all of the changes, but if we have forgot any and you get an error saying something should be a function where you have provided a constant it is safe to assume you simply need to add `()` to your code for it to work again.
  - 💥 **BREAKING CHANGE:** `PickDefined` utility type has been removed.
  - 💥 **BREAKING CHANGE:** `PickUndefinedMakeOptional` utility type has been removed.
  - 💥 **BREAKING CHANGE:** `NonNullObject` utility type has been removed.
  - 💥 **BREAKING CHANGE:** `s.any` is now `s.any()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthEqual` is now `s.array(T).lengthEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthGreaterThan` is now `s.array(T).lengthGreaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthGreaterThanOrEqual` is now `s.array(T).lengthGreaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthLessThan` is now `s.array(T).lengthLessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthLessThanOrEqual` is now `s.array(T).lengthLessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthNotEqual` is now `s.array(T).lengthNotEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthRange` is now `s.array(T).lengthRange()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthRangeExclusive` is now `s.array(T).lengthRangeExclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).lengthRangeInclusive` is now `s.array(T).lengthRangeInclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array(T).unique` is now `s.array(T).unique()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.array` is now `s.array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.divisibleBy` is now `s.bigint().divisibleBy()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.equal` is now `s.bigint().equal()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.greaterThan` is now `s.bigint().greaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.greaterThanOrEqual` is now `s.bigint().greaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.lessThan` is now `s.bigint().lessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.lessThanOrEqual` is now `s.bigint().lessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint.notEqual` is now `s.bigint().notEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigint().abs` is now `s.bigint().abs()` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.bigint().negative` is now `s.bigint().negative()` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.bigint().positive` is now `s.bigint().positive()` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.bigint` is now `s.bigint()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigInt64Array` is now `s.bigInt64Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.bigUint64Array` is now `s.bigUint64Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.boolean.false` is now `s.boolean().false()` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.boolean.true` is now `s.boolean().true()` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.boolean` is now `s.boolean()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.default(...)` now gets a second parameter to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.default(...).default(...)` now gets a second parameter to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.equal` is now `s.date().equal()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.greaterThan` is now `s.date().greaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.greaterThanOrEqual` is now `s.date().greaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.invalid` is now `s.date().invalid()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.lessThan` is now `s.date().lessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.lessThanOrEqual` is now `s.date().lessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.notEqual` is now `s.date().notEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date.valid` is now `s.date().valid()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.date` is now `s.date()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.enum(1, 2, 3)` is now `s.enum([1, 2, 3])` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.float32Array` is now `s.float32Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.float64Array` is now `s.float64Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.int16Array` is now `s.int16Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.int32Array` is now `s.int32Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.int8Array` is now `s.int8Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.never` is now `s.never()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.null` is now `s.null()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.nullable` is now `s.nullable()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.nullish` is now `s.nullish()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.nullish` is now `s.nullish()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.abs` is now `s.number().abs()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.ceil` is now `s.number().ceil()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.divisibleBy` is now `s.number().divisibleBy()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.equal` is now `s.number().equal()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.finite` is now `s.number().finite()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.floor` is now `s.number().floor()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.fround` is now `s.number().fround()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.greaterThan` is now `s.number().greaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.greaterThanOrEqual` is now `s.number().greaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.int` is now `s.number().int()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.lessThan` is now `s.number().lessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.lessThanOrEqual` is now `s.number().lessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.negative` is now `s.number().negative()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.notEqual` is now `s.number().notEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.positive` is now `s.number().positive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.round` is now `s.number().round()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.safeInt` is now `s.number().safeInt()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.sign` is now `s.number().sign()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number.trunc` is now `s.number().trunc()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.number` is now `s.number()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.object.ignore` is now `s.object().ignore()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.object.partial` is now `s.object().partial()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.object.passthrough` is now `s.object().passthrough()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.object.required` is now `s.object().required()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.object.strict` is now `s.object().strict()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.optional` is now `s.optional()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.required(...)` now gets a second parameter to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.set` is now `s.set()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string.date` is now `s.string().date()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string.email` is now `s.string().email()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string.ipv4` is now `s.string().ipv4()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string.ipv6` is now `s.string().ipv6()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().ip` is now `s.string().ip()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthEqual` is now `s.string().lengthEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthGreaterThan` is now `s.string().lengthGreaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthGreaterThanOrEqual` is now `s.string().lengthGreaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthLessThan` is now `s.string().lengthLessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthLessThanOrEqual` is now `s.string().lengthLessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().lengthNotEqual` is now `s.string().lengthNotEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().phone` is now `s.string().phone()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().regex` is now `s.string().regex()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string().url` is now `s.string().url()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.string` is now `s.string()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.tuple(1, 2, 3)` is now `s.tuple([1, 2, 3])` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthEqual` is now `s.typedArray(T).byteLengthEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthGreaterThan` is now `s.typedArray(T).byteLengthGreaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthGreaterThanOrEqual` is now `s.typedArray(T).byteLengthGreaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthLessThan` is now `s.typedArray(T).byteLengthLessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthLessThanOrEqual` is now `s.typedArray(T).byteLengthLessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthNotEqual` is now `s.typedArray(T).byteLengthNotEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthRange` is now `s.typedArray(T).byteLengthRange()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthRangeExclusive` is now `s.typedArray(T).byteLengthRangeExclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).byteLengthRangeInclusive` is now `s.typedArray(T).byteLengthRangeInclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthEqual` is now `s.typedArray(T).lengthEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthGreaterThan` is now `s.typedArray(T).lengthGreaterThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthGreaterThanOrEqual` is now `s.typedArray(T).lengthGreaterThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthLessThan` is now `s.typedArray(T).lengthLessThan()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthLessThanOrEqual` is now `s.typedArray(T).lengthLessThanOrEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthNotEqual` is now `s.typedArray(T).lengthNotEqual()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthRange` is now `s.typedArray(T).lengthRange()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthRangeExclusive` is now `s.typedArray(T).lengthRangeExclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.typedArray(T).lengthRangeInclusive` is now `s.typedArray(T).lengthRangeInclusive()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.uint16Array` is now `s.uint16Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.uint32Array` is now `s.uint32Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.uint8Array` is now `s.uint8Array()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.uint8ClampedArray` is now `s.uint8ClampedArray()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.undefined` is now `s.undefined()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.union(1, 2, 3).required` is now `s.union(1, 2, 3).required()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `s.union(1, 2, 3)` is now `s.union([1, 2, 3])` to allow for custom options as second argument.
  - 💥 **BREAKING CHANGE:** `s.unknown` is now `s.unknown()` to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `uniqueArray` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `dateInvalid` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `dateValid` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `numberFinite` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `numberInt` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `numberNaN` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `numberNotNaN` is now a function (instead of a constant) to allow for custom options as argument.
  - 💥 **BREAKING CHANGE:** `numberSafeInt` is now a function (instead of a constant) to allow for custom options as argument.

# [3.9.7](https://github.com/sapphiredev/shapeshift/compare/v3.9.6...v3.9.7) - (2024-03-31)

## 🐛 Bug Fixes

- Allow engines.node >= 16 ([a4d8c8d](https://github.com/sapphiredev/shapeshift/commit/a4d8c8d70e414192b61d565f89e51b19a117c8c1))

# [3.9.6](https://github.com/sapphiredev/shapeshift/compare/v3.9.6...v3.9.6) - (2024-01-19)

## 🐛 Bug Fixes

- Fixed commonjs typings export mapping (#341) ([a5518aa](https://github.com/sapphiredev/shapeshift/commit/a5518aa88350925ad03f612e49f695a296a0d3b3))

# [3.9.5](https://github.com/sapphiredev/shapeshift/compare/v3.9.5...v3.9.5) - (2023-12-15)

## 🐛 Bug Fixes

- Properly publish all dist files ([8e925fa](https://github.com/sapphiredev/shapeshift/commit/8e925faff5a67ccca84c41473bf77c9e0a01ebc0))

# [3.9.4](https://github.com/sapphiredev/shapeshift/compare/v3.9.4...v3.9.4) - (2023-12-04)

## 🐛 Bug Fixes

- Properly split CJS and ESM ([9bb1ff9](https://github.com/sapphiredev/shapeshift/commit/9bb1ff95f2852e765a27c7a839ff5145ee6e3a01))

## 🧪 Testing

- Update path to global file ([d03b19f](https://github.com/sapphiredev/shapeshift/commit/d03b19f20507690869fe6b8f59a2400d95bd02be))

# [3.9.3](https://github.com/sapphiredev/shapeshift/compare/v3.9.3...v3.9.3) - (2023-10-13)

## 🏠 Refactor

- Change email regex (#306) ([c5d49cf](https://github.com/sapphiredev/shapeshift/commit/c5d49cf32931aff24ab74ba87ee8d6d35f7231af))

# [3.9.2](https://github.com/sapphiredev/shapeshift/compare/v3.9.1...v3.9.2) - (2023-06-04)

## 🐛 Bug Fixes

- **arrayvalidator:** Fixed runaway type instantiation with TypeScript >=5.1 (#275) ([f59d901](https://github.com/sapphiredev/shapeshift/commit/f59d90112181e6625230c28e6a4f0f065ced6344))

# [3.9.1](https://github.com/sapphiredev/shapeshift/compare/v3.9.0...v3.9.1) - (2023-06-02)

## 🐛 Bug Fixes

- **types:** Move the `types` condition to the front (#273) ([5a3e202](https://github.com/sapphiredev/shapeshift/commit/5a3e202e9ceafb3d330a568e93c060dd5aac1dde))

# [3.9.0](https://github.com/sapphiredev/shapeshift/compare/v3.8.2...v3.9.0) - (2023-05-09)

## 🐛 Bug Fixes

- Resolve minor grammar mistake (#260) ([62df609](https://github.com/sapphiredev/shapeshift/commit/62df6094845ffa118aa93ea3c5f47f81f1c5d99f))

## 🚀 Features

- Add BaseValidator.describe (#267) ([d9e1a2d](https://github.com/sapphiredev/shapeshift/commit/d9e1a2d2f3c5e6378f0025becf8497138ee6d97c))

# [3.8.2](https://github.com/sapphiredev/shapeshift/compare/v3.8.1...v3.8.2) - (2023-04-02)

## 🐛 Bug Fixes

- ***:** TypeScript 5.x compatibility (#253) ([eba2a88](https://github.com/sapphiredev/shapeshift/commit/eba2a88b91fb6631f431313753299ec7a70cf6ce))
- Remove `node:` prefix (#249) ([af766b5](https://github.com/sapphiredev/shapeshift/commit/af766b504c1013f3cd24f7bf803ac9ff7442a8d7))

# [3.8.1](https://github.com/sapphiredev/shapeshift/compare/v3.8.0...v3.8.1) - (2022-12-15)

## 🐛 Bug Fixes

- Fixed lodash esm import (#230) ([63def7b](https://github.com/sapphiredev/shapeshift/commit/63def7bcec6319b3792093945ba7ba9f871ced6f))

# [3.8.0](https://github.com/sapphiredev/shapeshift/compare/v3.7.1...v3.8.0) - (2022-12-11)

## 🏠 Refactor

- Remove `NonNullObject` (#227) ([04d3934](https://github.com/sapphiredev/shapeshift/commit/04d39343f55a4e1571f54870a84d8b95447bd682))

## 🚀 Features

- Add `when` constraint (#223) ([8eade90](https://github.com/sapphiredev/shapeshift/commit/8eade90cd4c02b80746ecdcdc612829d7f765178))

# [3.7.1](https://github.com/sapphiredev/shapeshift/compare/v3.7.0...v3.7.1) - (2022-11-27)

## 🐛 Bug Fixes

- Fixed "jump to definition" for `undefinedToOptional` going to wrong symbol (#226) ([6aab6d0](https://github.com/sapphiredev/shapeshift/commit/6aab6d01450fd7abbeaa95e91fb58568240e02ff))

## 📝 Documentation

- Add @legendhimslef as a contributor ([499522a](https://github.com/sapphiredev/shapeshift/commit/499522a782c3ecd4df80978d0811df1a75d08212))

# [3.7.0](https://github.com/sapphiredev/shapeshift/compare/v3.6.0...v3.7.0) - (2022-10-02)

## 📝 Documentation

- Add phone in readme (#203) ([4ec9b7a](https://github.com/sapphiredev/shapeshift/commit/4ec9b7ab85124d84b3404cb548b17b9221a716c5))
- Add RealShadowNova as a contributor for tool (#185) ([6dfc442](https://github.com/sapphiredev/shapeshift/commit/6dfc442af6ef26d6bbca39078eca5727257b6dab))

## 🚀 Features

- Add `s.string.phone` (#202) ([7d122d5](https://github.com/sapphiredev/shapeshift/commit/7d122d5dc0eaa63c639b9cde1514e63566a681bd))

# [3.6.0](https://github.com/sapphiredev/shapeshift/compare/v3.5.1...v3.6.0) - (2022-08-29)

## 🐛 Bug Fixes

- Typescript 4.8 compatibility (#179) ([2281535](https://github.com/sapphiredev/shapeshift/commit/2281535f7589a987510828e46bf66accc8393c34))

## 🚀 Features

- Add `Validator#is` (#183) ([5114f95](https://github.com/sapphiredev/shapeshift/commit/5114f9516e5406cd1ca4a7ceb5ea5761158af1c6))

# [3.5.1](https://github.com/sapphiredev/shapeshift/compare/v3.5.0...v3.5.1) - (2022-07-17)

## 🐛 Bug Fixes

- Fast deep equal import (#155) ([5ce8ff6](https://github.com/sapphiredev/shapeshift/commit/5ce8ff6803b70624af07c3e406bc1cdc9e3cdafe))

# [3.5.0](https://github.com/sapphiredev/shapeshift/compare/v3.4.1...v3.5.0) - (2022-07-10)

## 🏠 Refactor

- Port net module (#149) ([5f26e32](https://github.com/sapphiredev/shapeshift/commit/5f26e32b0f87d2b100ca13471d5835c0067ddee8))

## 🐛 Bug Fixes

- Ensure browser compatibility (#150) ([92d05d8](https://github.com/sapphiredev/shapeshift/commit/92d05d83c1fbab53f98f61219fb01d49fc031bae))
- Fixed `s.array` type inference (#153) ([a5948dc](https://github.com/sapphiredev/shapeshift/commit/a5948dc67ce6a0ea73986d32084898a4ce0b9c3a))
- Fixed `shape#array` types (#146) ([43016a0](https://github.com/sapphiredev/shapeshift/commit/43016a025b04a676d906758ed065d26a17231888))

## 🚀 Features

- Lazy validator (#147) ([807666e](https://github.com/sapphiredev/shapeshift/commit/807666ef537c84d2e0f8bd9f4ce1a8060bfb3fb5))
- Reshape finally (#148) ([d3751f6](https://github.com/sapphiredev/shapeshift/commit/d3751f6d3d99f415d797369f98158f932371e02c))
- **arrays:** Add unique (#141) ([ad7af34](https://github.com/sapphiredev/shapeshift/commit/ad7af34eb811541253150b7ff0b58a6bd7200796))

# [3.4.1](https://github.com/sapphiredev/shapeshift/compare/v3.4.0...v3.4.1) - (2022-07-03)

## 🏠 Refactor

- Move all type utilities to one file (#139) ([61cab3d](https://github.com/sapphiredev/shapeshift/commit/61cab3d0e486d9dc74c8f6160ff8c75c91b595b2))

## 🐛 Bug Fixes

- Return array-validator from length* methods (#140) ([75b1f9a](https://github.com/sapphiredev/shapeshift/commit/75b1f9a6efffb6c27dcfd48eb4ec6269a3614633))

## 🧪 Testing

- Typechecking for tests (#145) ([273cdc8](https://github.com/sapphiredev/shapeshift/commit/273cdc82c1cf65ba4111ca6e70b050e02cbdf485))

# [3.4.0](https://github.com/sapphiredev/shapeshift/compare/v3.3.2...v3.4.0) - (2022-06-29)

## 🚀 Features

- Add `required` in object validation (#137) ([928f7be](https://github.com/sapphiredev/shapeshift/commit/928f7beb5e727b47868e9e46f2191f2def228080))

# [3.3.2](https://github.com/sapphiredev/shapeshift/compare/v3.3.1...v3.3.2) - (2022-06-26)

## 🐛 Bug Fixes

- Make keys optional in object parsing (#134) ([57a3719](https://github.com/sapphiredev/shapeshift/commit/57a37193d64399aae1431b041012d582e8defecf))

# [3.3.1](https://github.com/sapphiredev/shapeshift/compare/v3.3.0...v3.3.1) - (2022-06-22)

## 🐛 Bug Fixes

- Add generic type to parse (#133) ([90c91aa](https://github.com/sapphiredev/shapeshift/commit/90c91aad572d51a2bfbd1ed32a51e1d4201c5d4a))

# [3.3.0](https://github.com/sapphiredev/shapeshift/compare/v3.2.0...v3.3.0) - (2022-06-19)

## 🐛 Bug Fixes

- Compile for es2020 instead of es2021 (#128) ([051344d](https://github.com/sapphiredev/shapeshift/commit/051344debe1cf423713d7fc64b8908353348f301))

## 🚀 Features

- Allow passing functions in `setValidationEnabled` (#131) ([e1991cf](https://github.com/sapphiredev/shapeshift/commit/e1991cfef1ffe92f9167d11d7f2ded65379df8d2))

## 🧪 Testing

- Migrate to vitest (#126) ([4d80969](https://github.com/sapphiredev/shapeshift/commit/4d80969b714c39768499569456405a73c1444da8))

# [3.2.0](https://github.com/sapphiredev/shapeshift/compare/v3.1.0...v3.2.0) - (2022-06-11)

## 🚀 Features

- Add disabling of validators (#125) ([e17af95](https://github.com/sapphiredev/shapeshift/commit/e17af95d697be62796c57d03385b0c74b9d2d580))

# [3.1.0](https://github.com/sapphiredev/shapeshift/compare/v3.0.0...v3.1.0) - (2022-06-04)

## 🐛 Bug Fixes

- **ObjectValidator:** Fix #121 (#122) ([ecfad7e](https://github.com/sapphiredev/shapeshift/commit/ecfad7ec2cdd9e0cee0b3e227e55a91b28c29c30))

## 📝 Documentation

- **readme:** Clarify the difference between validations and schemas and add table of contents (#108) ([dc492a3](https://github.com/sapphiredev/shapeshift/commit/dc492a395349cc5bc680f313146127ea510b4ada))

## 🚀 Features

- **StringValidator:** Add date string checks (#106) ([1b72907](https://github.com/sapphiredev/shapeshift/commit/1b729078be32a88aeddf574c9cff3098990d4f94))

# [3.0.0](https://github.com/sapphiredev/shapeshift/compare/v2.2.0...v3.0.0) - (2022-05-06)

## 🏃 Performance

- Speed up object validation a LOT (#101) ([817278e](https://github.com/sapphiredev/shapeshift/commit/817278e6a3ac128ca342e5ae1737f40b98788c37))

## 🐛 Bug Fixes

- Expand method names (#100) ([741490f](https://github.com/sapphiredev/shapeshift/commit/741490fb6907f618fa25fe53808f7dcb5a59a96c))}

   ### 💥 Breaking Changes:
   - `date.eq` has been renamed to `date.equal`
   - `string.lengthLt` has been renamed to `string.lengthLessThan`
   - `string.lengthLe` has been renamed to `string.lengthLessThanOrEqual`
   - `string.lengthGt` has been renamed to `string.lengthGreaterThan`
   - `string.lengthGe` has been renamed to `string.lengthGreaterThanOrEqual`
   - `string.lengthEq` has been renamed to `string.lengthEqual`
   - `string.lengthNe` has been renamed to `string.lengthNotEqual`
   - `number.gt` has been renamed to `number.greaterThan`
   - `number.ge` has been renamed to `number.greaterThanOrEqual`
   - `number.lt` has been renamed to `number.lessThan`
   - `number.le` has been renamed to `number.lessThanOrEqual`
   - `number.eq` has been renamed to `number.equal`
   - `number.ne` has been renamed to `number.notEqual`
   - `bigint.gt` has been renamed to `bigint.greaterThan`
   - `bigint.ge` has been renamed to `bigint.greaterThanOrEqual`
   - `bigint.lt` has been renamed to `bigint.lessThan`
   - `bigint.le` has been renamed to `bigint.lessThanOrEqual`
   - `bigint.eq` has been renamed to `bigint.equal`
   - `bigint.ne` has been renamed to `bigint.notEqual`
   - `boolean.eq` has been renamed to `boolean.equal`
   - `boolean.ne` has been renamed to `boolean.notEqual`
   - `array.lengthLt` has been renamed to `array.lengthLessThan`
   - `array.lengthLe` has been renamed to `array.lengthLessThanOrEqual`
   - `array.lengthGt` has been renamed to `array.lengthGreaterThan`
   - `array.lengthGe` has been renamed to `array.lengthGreaterThanOrEqual`
   - `array.lengthEq` has been renamed to `array.lengthEqual`
   - `array.lengthNe` has been renamed to `array.lengthNotEqual`
   - `typedArray.lengthLt` has been renamed to `typedArray.lengthLessThan`
   - `typedArray.lengthLe` has been renamed to `typedArray.lengthLessThanOrEqual`
   - `typedArray.lengthGt` has been renamed to `typedArray.lengthGreaterThan`
   - `typedArray.lengthGe` has been renamed to `typedArray.lengthGreaterThanOrEqual`
   - `typedArray.lengthEq` has been renamed to `typedArray.lengthEqual`
   - `typedArray.lengthNe` has been renamed to `typedArray.lengthNotEqual`
   - `typedArray.byteLengthLt` has been renamed to `typedArray.byteLengthLessThan`
   - `typedArray.byteLengthLe` has been renamed to `typedArray.byteLengthLessThanOrEqual`
   - `typedArray.byteLengthGt` has been renamed to `typedArray.byteLengthGreaterThan`
   - `typedArray.byteLengthGe` has been renamed to `typedArray.byteLengthGreaterThanOrEqual`
   - `typedArray.byteLengthEq` has been renamed to `typedArray.byteLengthEqual`
   - `typedArray.byteLengthNe` has been renamed to `typedArray.byteLengthNotEqual`

- **ObjectValidator:** Don't run validation on arrays (#99) ([c83b3d0](https://github.com/sapphiredev/shapeshift/commit/c83b3d03a201d38cc230d9c831ca1d9b66ca533b))

## 🚀 Features

- Add 2 utility types inspired by yup and co (#102) ([2fef902](https://github.com/sapphiredev/shapeshift/commit/2fef9026c30f2f1825aa55511d0ab088a3dd13ab))

# [2.2.0](https://github.com/sapphiredev/shapeshift/compare/v2.0.0...v2.2.0) - (2022-04-29)

## Bug Fixes

- Ensure `BaseError` is exported as value (#95) ([335d799](https://github.com/sapphiredev/shapeshift/commit/335d799ef7a8c1a19a12e3eeec07e6d210db113d))

## Documentation

- **readme:** Add todo notice for `reshape` and `function` validations (#75) ([d5f16f6](https://github.com/sapphiredev/shapeshift/commit/d5f16f615de83503187dc834c6245fafbf138f5e))

## Features

- Add Typed Array (#78) ([ca5ea5f](https://github.com/sapphiredev/shapeshift/commit/ca5ea5f568084bd5d3aa004911d4ea64329e1a89))

## Performance

- Optimize `NativeEnum` (#79) ([e9ae280](https://github.com/sapphiredev/shapeshift/commit/e9ae280f73e9ea08239bd8bd22165fe0b2178f82))

# [@sapphire/shapeshift@2.1.0](https://github.com/sapphiredev/shapeshift/compare/v2.0.0...@sapphire/shapeshift@2.1.0) - (2022-04-24)

## Documentation

- **readme:** Add todo notice for `reshape` and `function` validations (#75) ([d5f16f6](https://github.com/sapphiredev/shapeshift/commit/d5f16f615de83503187dc834c6245fafbf138f5e))

## Performance

- Optimize `NativeEnum` (#79) ([e9ae280](https://github.com/sapphiredev/shapeshift/commit/e9ae280f73e9ea08239bd8bd22165fe0b2178f82))

## [2.0.0](https://github.com/sapphiredev/shapeshift/compare/v1.0.0...v2.0.0) (2022-03-13)

### Features

-   add `default` ([#25](https://github.com/sapphiredev/shapeshift/issues/25)) ([378c51f](https://github.com/sapphiredev/shapeshift/commit/378c51fb4ba2c501fd782387169db888d6bfe662))
-   add bigint methods ([#32](https://github.com/sapphiredev/shapeshift/issues/32)) ([4c444c1](https://github.com/sapphiredev/shapeshift/commit/4c444c15657c4610b99481b6dec9812bd136d72b))
-   add MapValidator ([#21](https://github.com/sapphiredev/shapeshift/issues/21)) ([c4d1258](https://github.com/sapphiredev/shapeshift/commit/c4d12586762d446b858454077b66635d9d11e2d6))
-   add NativeEnum validator ([#54](https://github.com/sapphiredev/shapeshift/issues/54)) ([7359042](https://github.com/sapphiredev/shapeshift/commit/7359042843d1119f396ac2c038aaff89dbd90c8e))
-   add RecordValidator ([#20](https://github.com/sapphiredev/shapeshift/issues/20)) ([8727427](https://github.com/sapphiredev/shapeshift/commit/8727427be4656792eebcdc5bddf6bcd61bc7e846))
-   add remaining string validations ([#38](https://github.com/sapphiredev/shapeshift/issues/38)) ([1c2fd7b](https://github.com/sapphiredev/shapeshift/commit/1c2fd7bbb20463f09ac461b697c312bec6ae9195))
-   add tuple ([#39](https://github.com/sapphiredev/shapeshift/issues/39)) ([b7704bf](https://github.com/sapphiredev/shapeshift/commit/b7704bf87cf5225021408cf4a6b9ceff8cba25b3))
-   added number transformers ([#17](https://github.com/sapphiredev/shapeshift/issues/17)) ([89a8ddd](https://github.com/sapphiredev/shapeshift/commit/89a8ddd8583774e68c43260c28ee1589ef44516c))
-   allow the use of module: NodeNext ([#55](https://github.com/sapphiredev/shapeshift/issues/55)) ([e6827c5](https://github.com/sapphiredev/shapeshift/commit/e6827c5a12b6a2803a137b71fe4c21bd1c35034b))
-   **array:** add array length Comparators ([#40](https://github.com/sapphiredev/shapeshift/issues/40)) ([1e564c2](https://github.com/sapphiredev/shapeshift/commit/1e564c204b6c9b0a798b3121d31dd4cc99165f60))
-   **Array:** generate tuple types with given length ([#52](https://github.com/sapphiredev/shapeshift/issues/52)) ([793648b](https://github.com/sapphiredev/shapeshift/commit/793648b4cde1f72c5b735ceebb0c48272179be06))
-   **ArrayValidator:** add length ranges ([#53](https://github.com/sapphiredev/shapeshift/issues/53)) ([e431d62](https://github.com/sapphiredev/shapeshift/commit/e431d6218b86fc1480fce14c4466cb36567cad2f))
-   display the property that errored ([#35](https://github.com/sapphiredev/shapeshift/issues/35)) ([fe188b0](https://github.com/sapphiredev/shapeshift/commit/fe188b0d17eeaa5f73b08085562903e23e91717c))
-   improve how errors are returned ([#29](https://github.com/sapphiredev/shapeshift/issues/29)) ([8bc7669](https://github.com/sapphiredev/shapeshift/commit/8bc7669a1a66a10449b321cc4447e411383977d9))
-   **s.object:** add passthrough ([#66](https://github.com/sapphiredev/shapeshift/issues/66)) ([ee9f6f3](https://github.com/sapphiredev/shapeshift/commit/ee9f6f367e513c0120a04cfafe05057c7907c327))

### Bug Fixes

-   copy/paste error and `ge` ([#22](https://github.com/sapphiredev/shapeshift/issues/22)) ([fe6505f](https://github.com/sapphiredev/shapeshift/commit/fe6505f8e698bcaf9f8024b2d8f012559827cbb0))
-   fix union type and add test ([#41](https://github.com/sapphiredev/shapeshift/issues/41)) ([fbcf8a9](https://github.com/sapphiredev/shapeshift/commit/fbcf8a9c617c16b33fdddb0a44aa0fe506164fd3))
-   **s.union:** fix union overrides ([#62](https://github.com/sapphiredev/shapeshift/issues/62)) ([56e9b19](https://github.com/sapphiredev/shapeshift/commit/56e9b1947d9b2b129dbed374671114b2242e6d35))

## 1.0.0 (2022-01-16)

### Features

-   added more primitives ([#2](https://github.com/sapphiredev/shapeshift/issues/2)) ([16af17b](https://github.com/sapphiredev/shapeshift/commit/16af17b5d9ee40dce284ee120e0b099f7b2cc0b8))
-   added more things ([7c73d82](https://github.com/sapphiredev/shapeshift/commit/7c73d82cf3740d5b2d4eebcac7767da9d3562437))
-   added ObjectValidator ([#3](https://github.com/sapphiredev/shapeshift/issues/3)) ([abe7ead](https://github.com/sapphiredev/shapeshift/commit/abe7eaddee981ef485713ff5e7b7f32ff97c645b))

### Bug Fixes

-   resolved install error ([a5abe13](https://github.com/sapphiredev/shapeshift/commit/a5abe1362bb6d9ce6d6471bffa47fe8983b0d1a4))
